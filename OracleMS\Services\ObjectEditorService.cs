using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Oracle.ManagedDataAccess.Client;
using OracleMS.Exceptions;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.Services
{
    /// <summary>
    /// 資料庫物件編輯服務實作
    /// </summary>
    public class ObjectEditorService : IObjectEditorService
    {
        private readonly IDatabaseRepository _databaseRepository;
        private readonly ILogger<ObjectEditorService> _logger;

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="databaseRepository">資料庫儲存庫</param>
        /// <param name="logger">記錄器</param>
        public ObjectEditorService(IDatabaseRepository databaseRepository, ILogger<ObjectEditorService> logger)
        {
            _databaseRepository = databaseRepository ?? throw new ArgumentNullException(nameof(databaseRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region Table Operations

        /// <summary>
        /// 取得資料表定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="tableName">資料表名稱</param>
        /// <returns>資料表定義</returns>
        public async Task<TableDefinition> GetTableDefinitionAsync(IDbConnection connection, string tableName)
        {
            try
            {
                _logger.LogInformation("正在取得資料表定義: {TableName}", tableName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(tableName))
                    throw new ArgumentException("資料表名稱不能為空", nameof(tableName));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 取得資料表結構
                var schema = await _databaseRepository.GetTableSchemaAsync(connection, tableName);

                // 建立資料表定義
                var tableDefinition = new TableDefinition
                {
                    Name = tableName,
                    Owner = schema.Owner
                };

                // 設定欄位
                foreach (var column in schema.Columns)
                {
                    tableDefinition.Columns.Add(new ColumnDefinition
                    {
                        Name = column.ColumnName,
                        DataType = column.DataType,
                        Length = column.MaxLength,
                        Precision = column.Precision,
                        Scale = column.Scale,
                        IsNullable = column.IsNullable,
                        DefaultValue = column.DefaultValue,
                        Comments = column.Comment
                    });
                }

                // 取得索引
                tableDefinition.Indexes = await GetTableIndexesAsync(connection, tableName);

                // 取得約束條件
                tableDefinition.Constraints = await GetTableConstraintsAsync(connection, tableName);

                // 取得觸發器
                tableDefinition.Triggers = await GetTableTriggersAsync(connection, tableName);

                _logger.LogInformation("成功取得資料表定義: {TableName}", tableName);
                return tableDefinition;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得資料表定義失敗: {TableName}", tableName);
                throw new OracleManagementException($"取得資料表定義失敗: {ex.Message}", ex);
            }
        }  
      /// <summary>
        /// 儲存資料表定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">資料表定義</param>
        /// <returns>非同步工作</returns>
        public async Task SaveTableDefinitionAsync(IDbConnection connection, TableDefinition definition)
        {
            try
            {
                _logger.LogInformation("正在儲存資料表定義: {TableName}", definition.Name);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (definition == null)
                    throw new ArgumentNullException(nameof(definition));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 驗證資料表定義
                var validationResult = definition.Validate();
                if (!validationResult.IsValid)
                {
                    throw new ArgumentException($"資料表定義無效: {string.Join(", ", validationResult.Errors)}");
                }

                // 檢查資料表是否存在
                bool tableExists = await TableExistsAsync(connection, definition.Name);

                if (tableExists)
                {
                    // 更新現有資料表
                    await UpdateTableAsync(connection, definition);
                }
                else
                {
                    // 創建新資料表
                    await CreateTableAsync(connection, definition);
                }

                _logger.LogInformation("成功儲存資料表定義: {TableName}", definition.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "儲存資料表定義失敗: {TableName}", definition.Name);
                throw new OracleManagementException($"儲存資料表定義失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 檢查資料表是否存在
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="tableName">資料表名稱</param>
        /// <returns>是否存在</returns>
        private async Task<bool> TableExistsAsync(IDbConnection connection, string tableName)
        {
            try
            {
                var sql = @"
                    SELECT COUNT(*) 
                    FROM ALL_TABLES 
                    WHERE TABLE_NAME = :tableName";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var parameter = command.CreateParameter();
                parameter.ParameterName = ":tableName";
                parameter.Value = tableName.ToUpper();
                command.Parameters.Add(parameter);

                var result = await Task.Run(() => command.ExecuteScalar());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檢查資料表是否存在失敗: {TableName}", tableName);
                throw new OracleManagementException($"檢查資料表是否存在失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 創建新資料表
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">資料表定義</param>
        /// <returns>非同步工作</returns>
        private async Task CreateTableAsync(IDbConnection connection, TableDefinition definition)
        {
            try
            {
                _logger.LogInformation("正在創建資料表: {TableName}", definition.Name);

                // 產生 CREATE TABLE 語句
                var sql = GenerateCreateTableSql(definition);

                // 執行 SQL
                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                // 創建索引
                foreach (var index in definition.Indexes)
                {
                    await CreateIndexInternalAsync(connection, index);
                }

                // 創建約束條件
                foreach (var constraint in definition.Constraints)
                {
                    await CreateConstraintAsync(connection, constraint);
                }

                // 創建觸發器
                foreach (var trigger in definition.Triggers)
                {
                    await SaveTriggerDefinitionAsync(connection, trigger);
                }

                _logger.LogInformation("成功創建資料表: {TableName}", definition.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "創建資料表失敗: {TableName}", definition.Name);
                throw new OracleManagementException($"創建資料表失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新現有資料表
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">資料表定義</param>
        /// <returns>非同步工作</returns>
        private async Task UpdateTableAsync(IDbConnection connection, TableDefinition definition)
        {
            try
            {
                _logger.LogInformation("正在更新資料表: {TableName}", definition.Name);

                // 取得現有資料表定義
                var existingDefinition = await GetTableDefinitionAsync(connection, definition.Name);

                // 比較並產生 ALTER TABLE 語句
                var alterSqlList = GenerateAlterTableSql(existingDefinition, definition);

                // 執行 SQL
                foreach (var sql in alterSqlList)
                {
                    await _databaseRepository.ExecuteNonQueryAsync(connection, sql);
                }

                // 更新索引
                await UpdateIndexesAsync(connection, existingDefinition.Indexes, definition.Indexes);

                // 更新約束條件
                await UpdateConstraintsAsync(connection, existingDefinition.Constraints, definition.Constraints);

                // 更新觸發器
                await UpdateTriggersAsync(connection, existingDefinition.Triggers, definition.Triggers);

                _logger.LogInformation("成功更新資料表: {TableName}", definition.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新資料表失敗: {TableName}", definition.Name);
                throw new OracleManagementException($"更新資料表失敗: {ex.Message}", ex);
            }
        }  
      /// <summary>
        /// 產生 CREATE TABLE 語句
        /// </summary>
        /// <param name="definition">資料表定義</param>
        /// <returns>SQL 語句</returns>
        private string GenerateCreateTableSql(TableDefinition definition)
        {
            var sql = new StringBuilder();
            sql.AppendLine($"CREATE TABLE {definition.Name} (");

            // 欄位定義
            var columnDefinitions = new List<string>();
            foreach (var column in definition.Columns)
            {
                var columnSql = $"  {column.Name} {column.GetFullDataType()}";
                
                if (!column.IsNullable)
                {
                    columnSql += " NOT NULL";
                }

                if (!string.IsNullOrWhiteSpace(column.DefaultValue))
                {
                    columnSql += $" DEFAULT {column.DefaultValue}";
                }

                columnDefinitions.Add(columnSql);
            }

            sql.AppendLine(string.Join(",\n", columnDefinitions));
            sql.AppendLine(")");

            return sql.ToString();
        }

        /// <summary>
        /// 產生 ALTER TABLE 語句
        /// </summary>
        /// <param name="existingDefinition">現有資料表定義</param>
        /// <param name="newDefinition">新資料表定義</param>
        /// <returns>SQL 語句清單</returns>
        private List<string> GenerateAlterTableSql(TableDefinition existingDefinition, TableDefinition newDefinition)
        {
            var sqlList = new List<string>();

            // 比較欄位
            var existingColumns = existingDefinition.Columns.ToDictionary(c => c.Name.ToUpper());
            var newColumns = newDefinition.Columns.ToDictionary(c => c.Name.ToUpper());

            // 新增欄位
            foreach (var column in newDefinition.Columns)
            {
                if (!existingColumns.ContainsKey(column.Name.ToUpper()))
                {
                    var sql = $"ALTER TABLE {newDefinition.Name} ADD {column.Name} {column.GetFullDataType()}";
                    
                    if (!column.IsNullable)
                    {
                        sql += " NOT NULL";
                    }

                    if (!string.IsNullOrWhiteSpace(column.DefaultValue))
                    {
                        sql += $" DEFAULT {column.DefaultValue}";
                    }

                    sqlList.Add(sql);
                }
            }

            // 修改欄位
            foreach (var column in newDefinition.Columns)
            {
                if (existingColumns.TryGetValue(column.Name.ToUpper(), out var existingColumn))
                {
                    // 檢查資料類型是否變更
                    if (column.GetFullDataType() != existingColumn.GetFullDataType())
                    {
                        sqlList.Add($"ALTER TABLE {newDefinition.Name} MODIFY {column.Name} {column.GetFullDataType()}");
                    }

                    // 檢查可空性是否變更
                    if (column.IsNullable != existingColumn.IsNullable)
                    {
                        var nullableSql = column.IsNullable ? "NULL" : "NOT NULL";
                        sqlList.Add($"ALTER TABLE {newDefinition.Name} MODIFY {column.Name} {nullableSql}");
                    }

                    // 檢查預設值是否變更
                    if (column.DefaultValue != existingColumn.DefaultValue)
                    {
                        if (string.IsNullOrWhiteSpace(column.DefaultValue))
                        {
                            sqlList.Add($"ALTER TABLE {newDefinition.Name} MODIFY {column.Name} DEFAULT NULL");
                        }
                        else
                        {
                            sqlList.Add($"ALTER TABLE {newDefinition.Name} MODIFY {column.Name} DEFAULT {column.DefaultValue}");
                        }
                    }
                }
            }

            // 刪除欄位
            foreach (var column in existingDefinition.Columns)
            {
                if (!newColumns.ContainsKey(column.Name.ToUpper()))
                {
                    sqlList.Add($"ALTER TABLE {newDefinition.Name} DROP COLUMN {column.Name}");
                }
            }

            return sqlList;
        }

        /// <summary>
        /// 取得資料表索引
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="tableName">資料表名稱</param>
        /// <returns>索引集合</returns>
        private async Task<ObservableCollection<IndexDefinition>> GetTableIndexesAsync(IDbConnection connection, string tableName)
        {
            try
            {
                var indexes = new ObservableCollection<IndexDefinition>();

                var sql = @"
                    SELECT 
                        i.INDEX_NAME, 
                        i.INDEX_TYPE, 
                        i.UNIQUENESS, 
                        i.STATUS, 
                        i.TABLESPACE_NAME,
                        i.LAST_ANALYZED,
                        i.DISTINCT_KEYS,
                        i.LEAF_BLOCKS,
                        i.CLUSTERING_FACTOR
                    FROM 
                        ALL_INDEXES i
                    WHERE 
                        i.TABLE_NAME = :tableName
                    ORDER BY 
                        i.INDEX_NAME";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var parameter = command.CreateParameter();
                parameter.ParameterName = ":tableName";
                parameter.Value = tableName.ToUpper();
                command.Parameters.Add(parameter);

                using var reader = await Task.Run(() => command.ExecuteReader());
                while (await Task.Run(() => reader.Read()))
                {
                    var indexName = reader["INDEX_NAME"].ToString();
                    var indexType = reader["INDEX_TYPE"].ToString();
                    var uniqueness = reader["UNIQUENESS"].ToString();
                    var status = reader["STATUS"].ToString();
                    var tablespace = reader["TABLESPACE_NAME"].ToString();
                    var lastAnalyzed = reader["LAST_ANALYZED"] as DateTime? ?? DateTime.MinValue;
                    var distinctKeys = Convert.ToInt32(reader["DISTINCT_KEYS"]);
                    var leafBlocks = Convert.ToInt32(reader["LEAF_BLOCKS"]);
                    var clustering = Convert.ToInt32(reader["CLUSTERING_FACTOR"]);

                    var index = new IndexDefinition
                    {
                        Name = indexName,
                        TableName = tableName,
                        Type = ParseIndexType(indexType),
                        IsUnique = uniqueness == "UNIQUE",
                        Status = status,
                        Tablespace = tablespace,
                        LastAnalyzed = lastAnalyzed,
                        DistinctKeys = distinctKeys,
                        LeafBlocks = leafBlocks,
                        Clustering = clustering
                    };

                    // 取得索引欄位
                    index.Columns = await GetIndexColumnsAsync(connection, indexName);

                    indexes.Add(index);
                }

                return indexes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得資料表索引失敗: {TableName}", tableName);
                throw new OracleManagementException($"取得資料表索引失敗: {ex.Message}", ex);
            }
        }
   /// <summary>
        /// 取得索引欄位
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="indexName">索引名稱</param>
        /// <returns>索引欄位集合</returns>
        private async Task<List<IndexColumnDefinition>> GetIndexColumnsAsync(IDbConnection connection, string indexName)
        {
            return await GetIndexColumnsAsync(connection, indexName, null);
        }

        /// <summary>
        /// 取得索引欄位
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="indexName">索引名稱</param>
        /// <param name="schemaName">Schema名稱（可選）</param>
        /// <returns>索引欄位集合</returns>
        private async Task<List<IndexColumnDefinition>> GetIndexColumnsAsync(IDbConnection connection, string indexName, string? schemaName)
        {
            try
            {
                _logger.LogInformation("正在取得索引欄位: {IndexName}, Schema: {Schema}", indexName, schemaName ?? "未指定");

                var columns = new List<IndexColumnDefinition>();

                // 根據是否有指定 Schema 來建構 SQL 查詢
                var sql = string.IsNullOrEmpty(schemaName)
                    ? @"SELECT
                        ic.COLUMN_NAME,
                        ic.COLUMN_POSITION,
                        ic.DESCEND
                    FROM
                        ALL_IND_COLUMNS ic
                    WHERE
                        ic.INDEX_NAME = :indexName
                    ORDER BY
                        ic.COLUMN_POSITION"
                    : @"SELECT
                        ic.COLUMN_NAME,
                        ic.COLUMN_POSITION,
                        ic.DESCEND
                    FROM
                        ALL_IND_COLUMNS ic
                    WHERE
                        ic.INDEX_NAME = :indexName
                        AND ic.INDEX_OWNER = :schemaName
                    ORDER BY
                        ic.COLUMN_POSITION";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var indexParameter = command.CreateParameter();
                indexParameter.ParameterName = ":indexName";
                indexParameter.Value = indexName;
                command.Parameters.Add(indexParameter);

                if (!string.IsNullOrEmpty(schemaName))
                {
                    var schemaParameter = command.CreateParameter();
                    schemaParameter.ParameterName = ":schemaName";
                    schemaParameter.Value = schemaName.ToUpper();
                    command.Parameters.Add(schemaParameter);
                }

                using var reader = await Task.Run(() => command.ExecuteReader());
                while (await Task.Run(() => reader.Read()))
                {
                    var columnName = reader["COLUMN_NAME"].ToString();
                    var position = Convert.ToInt32(reader["COLUMN_POSITION"]);
                    var descend = reader["DESCEND"].ToString() == "DESC";

                    columns.Add(new IndexColumnDefinition
                    {
                        ColumnName = columnName,
                        Position = position,
                        IsDescending = descend
                    });
                }

                _logger.LogInformation("成功取得索引欄位: {IndexName}, 欄位數量: {Count}, 欄位: [{Columns}]",
                    indexName, columns.Count, string.Join(", ", columns.Select(c => $"{c.ColumnName}({c.Position})")));

                return columns;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得索引欄位失敗: {IndexName}, Schema: {Schema}", indexName, schemaName ?? "未指定");
                throw new OracleManagementException($"取得索引欄位失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 解析索引類型
        /// </summary>
        /// <param name="indexType">索引類型字串</param>
        /// <returns>索引類型</returns>
        private IndexType ParseIndexType(string indexType)
        {
            if (string.IsNullOrWhiteSpace(indexType))
                return IndexType.Normal;

            return indexType.ToUpper() switch
            {
                "NORMAL" => IndexType.Normal,
                "BITMAP" => IndexType.Bitmap,
                "FUNCTION-BASED NORMAL" => IndexType.Function,
                "FUNCTION-BASED BITMAP" => IndexType.Function,
                "DOMAIN" => IndexType.Domain,
                "SPATIAL" => IndexType.Spatial,
                _ => IndexType.Normal
            };
        }

        /// <summary>
        /// 取得資料表約束條件
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="tableName">資料表名稱</param>
        /// <returns>約束條件集合</returns>
        private async Task<ObservableCollection<ConstraintDefinition>> GetTableConstraintsAsync(IDbConnection connection, string tableName)
        {
            try
            {
                var constraints = new ObservableCollection<ConstraintDefinition>();

                var sql = @"
                    SELECT 
                        c.CONSTRAINT_NAME, 
                        c.CONSTRAINT_TYPE, 
                        c.TABLE_NAME,
                        c.R_CONSTRAINT_NAME,
                        c.STATUS,
                        c.SEARCH_CONDITION
                    FROM 
                        ALL_CONSTRAINTS c
                    WHERE 
                        c.TABLE_NAME = :tableName
                    ORDER BY 
                        c.CONSTRAINT_NAME";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var parameter = command.CreateParameter();
                parameter.ParameterName = ":tableName";
                parameter.Value = tableName.ToUpper();
                command.Parameters.Add(parameter);

                using var reader = await Task.Run(() => command.ExecuteReader());
                while (await Task.Run(() => reader.Read()))
                {
                    var constraintName = reader["CONSTRAINT_NAME"].ToString();
                    var constraintType = reader["CONSTRAINT_TYPE"].ToString();
                    var tableName2 = reader["TABLE_NAME"].ToString();
                    var rConstraintName = reader["R_CONSTRAINT_NAME"] as string;
                    var status = reader["STATUS"].ToString();
                    var searchCondition = reader["SEARCH_CONDITION"] as string;

                    var constraint = new ConstraintDefinition
                    {
                        Name = constraintName,
                        Type = ParseConstraintType(constraintType),
                        TableName = tableName2,
                        IsEnabled = status == "ENABLED",
                        CheckCondition = searchCondition ?? string.Empty
                    };

                    // 取得約束條件欄位
                    constraint.Columns = await GetConstraintColumnsAsync(connection, constraintName);

                    // 如果是外鍵，取得參考的資料表和欄位
                    if (constraint.Type == ConstraintType.ForeignKey && !string.IsNullOrWhiteSpace(rConstraintName))
                    {
                        var refInfo = await GetReferencedConstraintInfoAsync(connection, rConstraintName);
                        constraint.ReferencedTable = refInfo.Item1;
                        constraint.ReferencedColumns = refInfo.Item2;
                    }

                    constraints.Add(constraint);
                }

                return constraints;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得資料表約束條件失敗: {TableName}", tableName);
                throw new OracleManagementException($"取得資料表約束條件失敗: {ex.Message}", ex);
            }
        }      
  /// <summary>
        /// 取得約束條件欄位
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="constraintName">約束條件名稱</param>
        /// <returns>欄位名稱集合</returns>
        private async Task<List<string>> GetConstraintColumnsAsync(IDbConnection connection, string constraintName)
        {
            try
            {
                var columns = new List<string>();

                var sql = @"
                    SELECT 
                        cc.COLUMN_NAME
                    FROM 
                        ALL_CONS_COLUMNS cc
                    WHERE 
                        cc.CONSTRAINT_NAME = :constraintName
                    ORDER BY 
                        cc.POSITION";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var parameter = command.CreateParameter();
                parameter.ParameterName = ":constraintName";
                parameter.Value = constraintName.ToUpper();
                command.Parameters.Add(parameter);

                using var reader = await Task.Run(() => command.ExecuteReader());
                while (await Task.Run(() => reader.Read()))
                {
                    var columnName = reader["COLUMN_NAME"].ToString();
                    columns.Add(columnName);
                }

                return columns;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得約束條件欄位失敗: {ConstraintName}", constraintName);
                throw new OracleManagementException($"取得約束條件欄位失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 取得參考的約束條件資訊
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="rConstraintName">參考的約束條件名稱</param>
        /// <returns>參考的資料表和欄位</returns>
        private async Task<Tuple<string, List<string>>> GetReferencedConstraintInfoAsync(IDbConnection connection, string rConstraintName)
        {
            try
            {
                string refTable = string.Empty;
                var refColumns = new List<string>();

                // 取得參考的資料表
                var tableSql = @"
                    SELECT 
                        c.TABLE_NAME
                    FROM 
                        ALL_CONSTRAINTS c
                    WHERE 
                        c.CONSTRAINT_NAME = :constraintName";

                using (var command = connection.CreateCommand())
                {
                    command.CommandText = tableSql;

                    var parameter = command.CreateParameter();
                    parameter.ParameterName = ":constraintName";
                    parameter.Value = rConstraintName.ToUpper();
                    command.Parameters.Add(parameter);

                    var result = await Task.Run(() => command.ExecuteScalar());
                    refTable = result?.ToString() ?? string.Empty;
                }

                // 取得參考的欄位
                var columnSql = @"
                    SELECT 
                        cc.COLUMN_NAME
                    FROM 
                        ALL_CONS_COLUMNS cc
                    WHERE 
                        cc.CONSTRAINT_NAME = :constraintName
                    ORDER BY 
                        cc.POSITION";

                using (var command = connection.CreateCommand())
                {
                    command.CommandText = columnSql;

                    var parameter = command.CreateParameter();
                    parameter.ParameterName = ":constraintName";
                    parameter.Value = rConstraintName.ToUpper();
                    command.Parameters.Add(parameter);

                    using var reader = await Task.Run(() => command.ExecuteReader());
                    while (await Task.Run(() => reader.Read()))
                    {
                        var columnName = reader["COLUMN_NAME"].ToString();
                        refColumns.Add(columnName);
                    }
                }

                return new Tuple<string, List<string>>(refTable, refColumns);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得參考的約束條件資訊失敗: {ConstraintName}", rConstraintName);
                throw new OracleManagementException($"取得參考的約束條件資訊失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 解析約束條件類型
        /// </summary>
        /// <param name="constraintType">約束條件類型字串</param>
        /// <returns>約束條件類型</returns>
        private ConstraintType ParseConstraintType(string constraintType)
        {
            if (string.IsNullOrWhiteSpace(constraintType))
                return ConstraintType.Check;

            return constraintType.ToUpper() switch
            {
                "P" => ConstraintType.PrimaryKey,
                "U" => ConstraintType.Unique,
                "R" => ConstraintType.ForeignKey,
                "C" => ConstraintType.Check,
                _ => ConstraintType.Check
            };
        }

        /// <summary>
        /// 取得資料表觸發器
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="tableName">資料表名稱</param>
        /// <returns>觸發器集合</returns>
        private async Task<ObservableCollection<TriggerDefinition>> GetTableTriggersAsync(IDbConnection connection, string tableName)
        {
            try
            {
                var triggers = new ObservableCollection<TriggerDefinition>();

                var sql = @"
                    SELECT 
                        t.TRIGGER_NAME, 
                        t.TRIGGER_TYPE, 
                        t.TRIGGERING_EVENT,
                        t.TABLE_NAME,
                        t.TRIGGER_BODY,
                        t.STATUS,
                        t.WHEN_CLAUSE
                    FROM 
                        ALL_TRIGGERS t
                    WHERE 
                        t.TABLE_NAME = :tableName
                    ORDER BY 
                        t.TRIGGER_NAME";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var parameter = command.CreateParameter();
                parameter.ParameterName = ":tableName";
                parameter.Value = tableName.ToUpper();
                command.Parameters.Add(parameter);

                using var reader = await Task.Run(() => command.ExecuteReader());
                while (await Task.Run(() => reader.Read()))
                {
                    var triggerName = reader["TRIGGER_NAME"].ToString();
                    var triggerType = reader["TRIGGER_TYPE"].ToString();
                    var triggeringEvent = reader["TRIGGERING_EVENT"].ToString();
                    var tableName2 = reader["TABLE_NAME"].ToString();
                    var triggerBody = reader["TRIGGER_BODY"].ToString();
                    var status = reader["STATUS"].ToString();
                    var whenClause = reader["WHEN_CLAUSE"] as string;

                    var trigger = new TriggerDefinition
                    {
                        Name = triggerName,
                        TableName = tableName2,
                        Type = ParseTriggerType(triggerType),
                        Event = ParseTriggerEvent(triggeringEvent),
                        Timing = ParseTriggerTiming(triggerType),
                        Body = triggerBody,
                        IsEnabled = status == "ENABLED",
                        Status = status,
                        Condition = whenClause ?? string.Empty
                    };

                    triggers.Add(trigger);
                }

                return triggers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得資料表觸發器失敗: {TableName}", tableName);
                throw new OracleManagementException($"取得資料表觸發器失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 解析觸發器類型
        /// </summary>
        /// <param name="triggerType">觸發器類型字串</param>
        /// <returns>觸發器類型</returns>
        private TriggerType ParseTriggerType(string triggerType)
        {
            if (string.IsNullOrWhiteSpace(triggerType))
                return TriggerType.Row;

            return triggerType.Contains("FOR EACH ROW") ? TriggerType.Row : TriggerType.Statement;
        }

        /// <summary>
        /// 解析觸發事件
        /// </summary>
        /// <param name="triggeringEvent">觸發事件字串</param>
        /// <returns>觸發事件</returns>
        private TriggerEvent ParseTriggerEvent(string triggeringEvent)
        {
            if (string.IsNullOrWhiteSpace(triggeringEvent))
                return TriggerEvent.Insert;

            var events = triggeringEvent.ToUpper().Split(" OR ");

            if (events.Contains("INSERT") && events.Contains("UPDATE") && events.Contains("DELETE"))
                return TriggerEvent.InsertOrUpdateOrDelete;
            else if (events.Contains("INSERT") && events.Contains("UPDATE"))
                return TriggerEvent.InsertOrUpdate;
            else if (events.Contains("INSERT") && events.Contains("DELETE"))
                return TriggerEvent.InsertOrDelete;
            else if (events.Contains("UPDATE") && events.Contains("DELETE"))
                return TriggerEvent.UpdateOrDelete;
            else if (events.Contains("INSERT"))
                return TriggerEvent.Insert;
            else if (events.Contains("UPDATE"))
                return TriggerEvent.Update;
            else if (events.Contains("DELETE"))
                return TriggerEvent.Delete;
            else
                return TriggerEvent.Insert;
        }

        /// <summary>
        /// 解析觸發時機
        /// </summary>
        /// <param name="triggerType">觸發器類型字串</param>
        /// <returns>觸發時機</returns>
        private TriggerTiming ParseTriggerTiming(string triggerType)
        {
            if (string.IsNullOrWhiteSpace(triggerType))
                return TriggerTiming.Before;

            if (triggerType.Contains("BEFORE"))
                return TriggerTiming.Before;
            else if (triggerType.Contains("AFTER"))
                return TriggerTiming.After;
            else if (triggerType.Contains("INSTEAD OF"))
                return TriggerTiming.Instead;
            else
                return TriggerTiming.Before;
        }

        /// <summary>
        /// 創建索引
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="index">索引定義</param>
        /// <returns>非同步工作</returns>
        private async Task CreateIndexInternalAsync(IDbConnection connection, IndexDefinition index)
        {
            try
            {
                _logger.LogInformation("正在創建索引: {IndexName}", index.Name);

                // 產生 CREATE INDEX 語句
                var sql = GenerateCreateIndexSql(index);

                // 執行 SQL
                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功創建索引: {IndexName}", index.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "創建索引失敗: {IndexName}", index.Name);
                throw new OracleManagementException($"創建索引失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 產生 CREATE INDEX 語句
        /// </summary>
        /// <param name="index">索引定義</param>
        /// <returns>SQL 語句</returns>
        private string GenerateCreateIndexSql(IndexDefinition index)
        {
            var sql = new StringBuilder();

            sql.Append($"CREATE ");

            if (index.IsUnique)
            {
                sql.Append("UNIQUE ");
            }

            if (index.Type == IndexType.Bitmap)
            {
                sql.Append("BITMAP ");
            }

            sql.Append($"INDEX {index.Name} ON {index.TableName} (");

            var columnDefinitions = new List<string>();
            foreach (var column in index.Columns.OrderBy(c => c.Position))
            {
                var columnSql = column.ColumnName;
                if (column.IsDescending)
                {
                    columnSql += " DESC";
                }
                columnDefinitions.Add(columnSql);
            }

            sql.Append(string.Join(", ", columnDefinitions));
            sql.Append(")");

            if (!string.IsNullOrWhiteSpace(index.Tablespace) && index.Tablespace.ToUpper() != "TO")
            {
                sql.Append($" TABLESPACE {index.Tablespace}");
            }

            return sql.ToString();
        }

        /// <summary>
        /// 創建約束條件
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="constraint">約束條件定義</param>
        /// <returns>非同步工作</returns>
        private async Task CreateConstraintAsync(IDbConnection connection, ConstraintDefinition constraint)
        {
            try
            {
                _logger.LogInformation("正在創建約束條件: {ConstraintName}", constraint.Name);

                // 產生 ALTER TABLE ADD CONSTRAINT 語句
                var sql = GenerateCreateConstraintSql(constraint);

                // 執行 SQL
                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功創建約束條件: {ConstraintName}", constraint.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "創建約束條件失敗: {ConstraintName}", constraint.Name);
                throw new OracleManagementException($"創建約束條件失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 產生 CREATE CONSTRAINT 語句
        /// </summary>
        /// <param name="constraint">約束條件定義</param>
        /// <returns>SQL 語句</returns>
        private string GenerateCreateConstraintSql(ConstraintDefinition constraint)
        {
            var sql = new StringBuilder();

            sql.Append($"ALTER TABLE {constraint.TableName} ADD CONSTRAINT {constraint.Name} ");

            switch (constraint.Type)
            {
                case ConstraintType.PrimaryKey:
                    sql.Append("PRIMARY KEY ");
                    sql.Append($"({string.Join(", ", constraint.Columns)})");
                    break;
                case ConstraintType.Unique:
                    sql.Append("UNIQUE ");
                    sql.Append($"({string.Join(", ", constraint.Columns)})");
                    break;
                case ConstraintType.ForeignKey:
                    sql.Append("FOREIGN KEY ");
                    sql.Append($"({string.Join(", ", constraint.Columns)}) ");
                    sql.Append($"REFERENCES {constraint.ReferencedTable} ");
                    sql.Append($"({string.Join(", ", constraint.ReferencedColumns)})");
                    break;
                case ConstraintType.Check:
                    sql.Append("CHECK ");
                    sql.Append($"({constraint.CheckCondition})");
                    break;
            }

            if (!constraint.IsEnabled)
            {
                sql.Append(" DISABLE");
            }

            return sql.ToString();
        }       
 /// <summary>
        /// 更新索引
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="existingIndexes">現有索引集合</param>
        /// <param name="newIndexes">新索引集合</param>
        /// <returns>非同步工作</returns>
        private async Task UpdateIndexesAsync(IDbConnection connection, IEnumerable<IndexDefinition> existingIndexes, IEnumerable<IndexDefinition> newIndexes)
        {
            try
            {
                _logger.LogInformation("正在更新索引");

                var existingDict = existingIndexes.ToDictionary(i => i.Name.ToUpper());
                var newDict = newIndexes.ToDictionary(i => i.Name.ToUpper());

                // 刪除不再需要的索引
                foreach (var index in existingIndexes)
                {
                    if (!newDict.ContainsKey(index.Name.ToUpper()))
                    {
                        await DropIndexAsync(connection, index.Name);
                    }
                }

                // 創建新索引
                foreach (var index in newIndexes)
                {
                    if (!existingDict.ContainsKey(index.Name.ToUpper()))
                    {
                        await CreateIndexInternalAsync(connection, index);
                    }
                    else
                    {
                        // 比較索引定義，如果不同則重建
                        var existingIndex = existingDict[index.Name.ToUpper()];
                        if (!IndexesEqual(existingIndex, index))
                        {
                            await DropIndexAsync(connection, index.Name);
                            await CreateIndexInternalAsync(connection, index);
                        }
                    }
                }

                _logger.LogInformation("成功更新索引");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新索引失敗");
                throw new OracleManagementException($"更新索引失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 刪除索引
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="indexName">索引名稱</param>
        /// <returns>非同步工作</returns>
        private async Task DropIndexAsync(IDbConnection connection, string indexName)
        {
            try
            {
                _logger.LogInformation("正在刪除索引: {IndexName}", indexName);

                var sql = $"DROP INDEX {indexName}";

                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功刪除索引: {IndexName}", indexName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除索引失敗: {IndexName}", indexName);
                throw new OracleManagementException($"刪除索引失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 比較兩個索引是否相同
        /// </summary>
        /// <param name="index1">索引1</param>
        /// <param name="index2">索引2</param>
        /// <returns>是否相同</returns>
        private bool IndexesEqual(IndexDefinition index1, IndexDefinition index2)
        {
            if (index1.IsUnique != index2.IsUnique)
                return false;

            if (index1.Type != index2.Type)
                return false;

            if (index1.Columns.Count != index2.Columns.Count)
                return false;

            for (int i = 0; i < index1.Columns.Count; i++)
            {
                var col1 = index1.Columns.FirstOrDefault(c => c.Position == i + 1);
                var col2 = index2.Columns.FirstOrDefault(c => c.Position == i + 1);

                if (col1 == null || col2 == null)
                    return false;

                if (col1.ColumnName.ToUpper() != col2.ColumnName.ToUpper())
                    return false;

                if (col1.IsDescending != col2.IsDescending)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 更新約束條件
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="existingConstraints">現有約束條件集合</param>
        /// <param name="newConstraints">新約束條件集合</param>
        /// <returns>非同步工作</returns>
        private async Task UpdateConstraintsAsync(IDbConnection connection, IEnumerable<ConstraintDefinition> existingConstraints, IEnumerable<ConstraintDefinition> newConstraints)
        {
            try
            {
                _logger.LogInformation("正在更新約束條件");

                var existingDict = existingConstraints.ToDictionary(c => c.Name.ToUpper());
                var newDict = newConstraints.ToDictionary(c => c.Name.ToUpper());

                // 刪除不再需要的約束條件
                foreach (var constraint in existingConstraints)
                {
                    if (!newDict.ContainsKey(constraint.Name.ToUpper()))
                    {
                        await DropConstraintAsync(connection, constraint.TableName, constraint.Name);
                    }
                }

                // 創建新約束條件
                foreach (var constraint in newConstraints)
                {
                    if (!existingDict.ContainsKey(constraint.Name.ToUpper()))
                    {
                        await CreateConstraintAsync(connection, constraint);
                    }
                    else
                    {
                        // 比較約束條件定義，如果不同則重建
                        var existingConstraint = existingDict[constraint.Name.ToUpper()];
                        if (!ConstraintsEqual(existingConstraint, constraint))
                        {
                            await DropConstraintAsync(connection, constraint.TableName, constraint.Name);
                            await CreateConstraintAsync(connection, constraint);
                        }
                        // 如果啟用狀態不同，則更新
                        else if (existingConstraint.IsEnabled != constraint.IsEnabled)
                        {
                            await UpdateConstraintStatusAsync(connection, constraint);
                        }
                    }
                }

                _logger.LogInformation("成功更新約束條件");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新約束條件失敗");
                throw new OracleManagementException($"更新約束條件失敗: {ex.Message}", ex);
            }
        }   
     /// <summary>
        /// 刪除約束條件
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="tableName">資料表名稱</param>
        /// <param name="constraintName">約束條件名稱</param>
        /// <returns>非同步工作</returns>
        private async Task DropConstraintAsync(IDbConnection connection, string tableName, string constraintName)
        {
            try
            {
                _logger.LogInformation("正在刪除約束條件: {ConstraintName}", constraintName);

                var sql = $"ALTER TABLE {tableName} DROP CONSTRAINT {constraintName}";

                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功刪除約束條件: {ConstraintName}", constraintName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除約束條件失敗: {ConstraintName}", constraintName);
                throw new OracleManagementException($"刪除約束條件失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新約束條件狀態
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="constraint">約束條件定義</param>
        /// <returns>非同步工作</returns>
        private async Task UpdateConstraintStatusAsync(IDbConnection connection, ConstraintDefinition constraint)
        {
            try
            {
                _logger.LogInformation("正在更新約束條件狀態: {ConstraintName}", constraint.Name);

                var status = constraint.IsEnabled ? "ENABLE" : "DISABLE";
                var sql = $"ALTER TABLE {constraint.TableName} MODIFY CONSTRAINT {constraint.Name} {status}";

                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功更新約束條件狀態: {ConstraintName}", constraint.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新約束條件狀態失敗: {ConstraintName}", constraint.Name);
                throw new OracleManagementException($"更新約束條件狀態失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 比較兩個約束條件是否相同
        /// </summary>
        /// <param name="constraint1">約束條件1</param>
        /// <param name="constraint2">約束條件2</param>
        /// <returns>是否相同</returns>
        private bool ConstraintsEqual(ConstraintDefinition constraint1, ConstraintDefinition constraint2)
        {
            if (constraint1.Type != constraint2.Type)
                return false;

            if (constraint1.Columns.Count != constraint2.Columns.Count)
                return false;

            for (int i = 0; i < constraint1.Columns.Count; i++)
            {
                if (constraint1.Columns[i].ToUpper() != constraint2.Columns[i].ToUpper())
                    return false;
            }

            if (constraint1.Type == ConstraintType.ForeignKey)
            {
                if (constraint1.ReferencedTable.ToUpper() != constraint2.ReferencedTable.ToUpper())
                    return false;

                if (constraint1.ReferencedColumns.Count != constraint2.ReferencedColumns.Count)
                    return false;

                for (int i = 0; i < constraint1.ReferencedColumns.Count; i++)
                {
                    if (constraint1.ReferencedColumns[i].ToUpper() != constraint2.ReferencedColumns[i].ToUpper())
                        return false;
                }
            }

            if (constraint1.Type == ConstraintType.Check)
            {
                if (constraint1.CheckCondition != constraint2.CheckCondition)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 更新觸發器
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="existingTriggers">現有觸發器集合</param>
        /// <param name="newTriggers">新觸發器集合</param>
        /// <returns>非同步工作</returns>
        private async Task UpdateTriggersAsync(IDbConnection connection, IEnumerable<TriggerDefinition> existingTriggers, IEnumerable<TriggerDefinition> newTriggers)
        {
            try
            {
                _logger.LogInformation("正在更新觸發器");

                var existingDict = existingTriggers.ToDictionary(t => t.Name.ToUpper());
                var newDict = newTriggers.ToDictionary(t => t.Name.ToUpper());

                // 刪除不再需要的觸發器
                foreach (var trigger in existingTriggers)
                {
                    if (!newDict.ContainsKey(trigger.Name.ToUpper()))
                    {
                        await DropTriggerAsync(connection, trigger.Name);
                    }
                }

                // 創建或更新觸發器
                foreach (var trigger in newTriggers)
                {
                    if (!existingDict.ContainsKey(trigger.Name.ToUpper()))
                    {
                        await SaveTriggerDefinitionAsync(connection, trigger);
                    }
                    else
                    {
                        // 比較觸發器定義，如果不同則重建
                        var existingTrigger = existingDict[trigger.Name.ToUpper()];
                        if (!TriggersEqual(existingTrigger, trigger))
                        {
                            await SaveTriggerDefinitionAsync(connection, trigger);
                        }
                        // 如果啟用狀態不同，則更新
                        else if (existingTrigger.IsEnabled != trigger.IsEnabled)
                        {
                            await UpdateTriggerStatusAsync(connection, trigger);
                        }
                    }
                }

                _logger.LogInformation("成功更新觸發器");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新觸發器失敗");
                throw new OracleManagementException($"更新觸發器失敗: {ex.Message}", ex);
            }
        }   
     /// <summary>
        /// 刪除觸發器
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="triggerName">觸發器名稱</param>
        /// <returns>非同步工作</returns>
        private async Task DropTriggerAsync(IDbConnection connection, string triggerName)
        {
            try
            {
                _logger.LogInformation("正在刪除觸發器: {TriggerName}", triggerName);

                var sql = $"DROP TRIGGER {triggerName}";

                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功刪除觸發器: {TriggerName}", triggerName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除觸發器失敗: {TriggerName}", triggerName);
                throw new OracleManagementException($"刪除觸發器失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新觸發器狀態
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="trigger">觸發器定義</param>
        /// <returns>非同步工作</returns>
        private async Task UpdateTriggerStatusAsync(IDbConnection connection, TriggerDefinition trigger)
        {
            try
            {
                _logger.LogInformation("正在更新觸發器狀態: {TriggerName}", trigger.Name);

                var status = trigger.IsEnabled ? "ENABLE" : "DISABLE";
                var sql = $"ALTER TRIGGER {trigger.Name} {status}";

                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功更新觸發器狀態: {TriggerName}", trigger.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新觸發器狀態失敗: {TriggerName}", trigger.Name);
                throw new OracleManagementException($"更新觸發器狀態失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 比較兩個觸發器是否相同
        /// </summary>
        /// <param name="trigger1">觸發器1</param>
        /// <param name="trigger2">觸發器2</param>
        /// <returns>是否相同</returns>
        private bool TriggersEqual(TriggerDefinition trigger1, TriggerDefinition trigger2)
        {
            if (trigger1.Type != trigger2.Type)
                return false;

            if (trigger1.Event != trigger2.Event)
                return false;

            if (trigger1.Timing != trigger2.Timing)
                return false;

            if (trigger1.Condition != trigger2.Condition)
                return false;

            if (trigger1.Body != trigger2.Body)
                return false;

            return true;
        }

        #endregion

        #region View Operations

        /// <summary>
        /// 取得檢視表定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="viewName">檢視表名稱</param>
        /// <returns>檢視表 SQL 定義</returns>
        public async Task<string> GetViewDefinitionAsync(IDbConnection connection, string viewName)
        {
            try
            {
                _logger.LogInformation("正在取得檢視表定義: {ViewName}", viewName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(viewName))
                    throw new ArgumentException("檢視表名稱不能為空", nameof(viewName));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 首先取得檢視表的擁有者
                var ownerSql = @"
                    SELECT OWNER
                    FROM ALL_VIEWS
                    WHERE VIEW_NAME = :viewName";

                string owner = "USER";
                using (var ownerCommand = connection.CreateCommand())
                {
                    ownerCommand.CommandText = ownerSql;
                    ownerCommand.Parameters.Add(new OracleParameter(":viewName", viewName.ToUpper()));

                    var ownerResult = await Task.Run(() => ownerCommand.ExecuteScalar());
                    if (ownerResult != null)
                    {
                        owner = ownerResult.ToString() ?? "USER";
                    }
                }

                // 使用 DBMS_METADATA.GET_DDL 取得完整的 DDL
                var sql = "SELECT DBMS_METADATA.GET_DDL('VIEW', :viewName, :owner) FROM DUAL";

                using var command = connection.CreateCommand();
                command.CommandText = sql;
                command.Parameters.Add(new OracleParameter(":viewName", viewName.ToUpper()));
                command.Parameters.Add(new OracleParameter(":owner", owner?.ToUpper() ?? "USER"));

                var viewDefinition = string.Empty;
                using var reader = await Task.Run(() => command.ExecuteReader());
                if (await Task.Run(() => reader.Read()))
                {
                    viewDefinition = reader.IsDBNull(0) ? string.Empty : reader.GetString(0);
                }

                _logger.LogInformation("成功取得檢視表定義: {ViewName}", viewName);
                return viewDefinition;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得檢視表定義失敗: {ViewName}", viewName);
                throw new OracleManagementException($"取得檢視表定義失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 儲存檢視表定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="viewName">檢視表名稱</param>
        /// <param name="definition">檢視表 SQL 定義</param>
        /// <returns>非同步工作</returns>
        public async Task SaveViewDefinitionAsync(IDbConnection connection, string viewName, string definition)
        {
            try
            {
                _logger.LogInformation("正在儲存檢視表定義: {ViewName}", viewName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(viewName))
                    throw new ArgumentException("檢視表名稱不能為空", nameof(viewName));

                if (string.IsNullOrWhiteSpace(definition))
                    throw new ArgumentException("檢視表定義不能為空", nameof(definition));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 檢查檢視表是否存在
                bool viewExists = await ViewExistsAsync(connection, viewName);

                // 產生 CREATE OR REPLACE VIEW 語句
                var sql = $"CREATE OR REPLACE VIEW {viewName} AS\n{definition}";

                // 執行 SQL
                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功儲存檢視表定義: {ViewName}", viewName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "儲存檢視表定義失敗: {ViewName}", viewName);
                throw new OracleManagementException($"儲存檢視表定義失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 檢查檢視表是否存在
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="viewName">檢視表名稱</param>
        /// <returns>是否存在</returns>
        private async Task<bool> ViewExistsAsync(IDbConnection connection, string viewName)
        {
            try
            {
                var sql = @"
                    SELECT COUNT(*) 
                    FROM ALL_VIEWS 
                    WHERE VIEW_NAME = :viewName";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var parameter = command.CreateParameter();
                parameter.ParameterName = ":viewName";
                parameter.Value = viewName.ToUpper();
                command.Parameters.Add(parameter);

                var result = await Task.Run(() => command.ExecuteScalar());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檢查檢視表是否存在失敗: {ViewName}", viewName);
                throw new OracleManagementException($"檢查檢視表是否存在失敗: {ex.Message}", ex);
            }
        }

        #endregion

        #region Procedure/Function Operations

        /// <summary>
        /// 取得預存程序原始碼
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="procedureName">預存程序名稱</param>
        /// <returns>預存程序原始碼</returns>
        public async Task<string> GetProcedureSourceAsync(IDbConnection connection, string procedureName)
        {
            try
            {
                _logger.LogInformation("正在取得預存程序原始碼: {ProcedureName}", procedureName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(procedureName))
                    throw new ArgumentException("預存程序名稱不能為空", nameof(procedureName));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                var source = await GetObjectSourceAsync(connection, procedureName, "PROCEDURE");

                _logger.LogInformation("成功取得預存程序原始碼: {ProcedureName}", procedureName);
                return source;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得預存程序原始碼失敗: {ProcedureName}", procedureName);
                throw new OracleManagementException($"取得預存程序原始碼失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 取得函數原始碼
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="functionName">函數名稱</param>
        /// <returns>函數原始碼</returns>
        public async Task<string> GetFunctionSourceAsync(IDbConnection connection, string functionName)
        {
            try
            {
                _logger.LogInformation("正在取得函數原始碼: {FunctionName}", functionName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(functionName))
                    throw new ArgumentException("函數名稱不能為空", nameof(functionName));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                var source = await GetObjectSourceAsync(connection, functionName, "FUNCTION");

                _logger.LogInformation("成功取得函數原始碼: {FunctionName}", functionName);
                _logger.LogInformation("返回函數原始碼: 長度={Length}, 內容預覽={Preview}",
                    source?.Length ?? 0, source?.Substring(0, Math.Min(50, source?.Length ?? 0)) ?? "null");
                return source;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得函數原始碼失敗: {FunctionName}", functionName);
                throw new OracleManagementException($"取得函數原始碼失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 取得物件原始碼
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="objectName">物件名稱</param>
        /// <param name="objectType">物件類型</param>
        /// <returns>物件原始碼</returns>
        private async Task<string> GetObjectSourceAsync(IDbConnection connection, string objectName, string objectType)
        {
            try
            {
                _logger.LogInformation("正在取得物件原始碼: {ObjectName}, 類型: {ObjectType}", objectName, objectType);

                var sql = @"
                    SELECT TEXT
                    FROM ALL_SOURCE
                    WHERE NAME = :objectName
                    AND TYPE = :objectType
                    AND OWNER = USER
                    ORDER BY LINE";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var nameParameter = command.CreateParameter();
                nameParameter.ParameterName = ":objectName";
                nameParameter.Value = objectName.ToUpper();
                command.Parameters.Add(nameParameter);

                var typeParameter = command.CreateParameter();
                typeParameter.ParameterName = ":objectType";
                typeParameter.Value = objectType;
                command.Parameters.Add(typeParameter);

                _logger.LogDebug("執行 SQL: {Sql}, 參數: NAME={ObjectName}, TYPE={ObjectType}",
                    sql, objectName.ToUpper(), objectType);

                var sourceBuilder = new StringBuilder();
                var lineCount = 0;
                using var reader = await Task.Run(() => command.ExecuteReader());
                while (await Task.Run(() => reader.Read()))
                {
                    var text = reader["TEXT"].ToString();
                    sourceBuilder.Append(text);
                    lineCount++;
                }

                var result = sourceBuilder.ToString();
                _logger.LogInformation("成功取得物件原始碼: {ObjectName}, 類型: {ObjectType}, 行數: {LineCount}, 內容長度: {ContentLength}",
                    objectName, objectType, lineCount, result.Length);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得物件原始碼失敗: {ObjectName}, {ObjectType}", objectName, objectType);
                throw new OracleManagementException($"取得物件原始碼失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 編譯預存程序
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="procedureName">預存程序名稱</param>
        /// <param name="source">預存程序原始碼</param>
        /// <returns>編譯結果</returns>
        public async Task<CompilationResult> CompileProcedureAsync(IDbConnection connection, string procedureName, string source)
        {
            try
            {
                _logger.LogInformation("正在編譯預存程序: {ProcedureName}", procedureName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(procedureName))
                    throw new ArgumentException("預存程序名稱不能為空", nameof(procedureName));

                if (string.IsNullOrWhiteSpace(source))
                    throw new ArgumentException("預存程序原始碼不能為空", nameof(source));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 執行 CREATE OR REPLACE PROCEDURE 語句
                try
                {
                    await _databaseRepository.ExecuteNonQueryAsync(connection, source);
                    _logger.LogInformation("成功編譯預存程序: {ProcedureName}", procedureName);
                    return CompilationResult.Success();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "編譯預存程序失敗: {ProcedureName}", procedureName);
                    return ParseCompilationError(ex);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "編譯預存程序失敗: {ProcedureName}", procedureName);
                return CompilationResult.Failure(ex.Message);
            }
        }

        /// <summary>
        /// 編譯函數
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="functionName">函數名稱</param>
        /// <param name="source">函數原始碼</param>
        /// <returns>編譯結果</returns>
        public async Task<CompilationResult> CompileFunctionAsync(IDbConnection connection, string functionName, string source)
        {
            try
            {
                _logger.LogInformation("正在編譯函數: {FunctionName}", functionName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(functionName))
                    throw new ArgumentException("函數名稱不能為空", nameof(functionName));

                if (string.IsNullOrWhiteSpace(source))
                    throw new ArgumentException("函數原始碼不能為空", nameof(source));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 執行 CREATE OR REPLACE FUNCTION 語句
                try
                {
                    await _databaseRepository.ExecuteNonQueryAsync(connection, source);
                    _logger.LogInformation("成功編譯函數: {FunctionName}", functionName);
                    return CompilationResult.Success();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "編譯函數失敗: {FunctionName}", functionName);
                    return ParseCompilationError(ex);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "編譯函數失敗: {FunctionName}", functionName);
                return CompilationResult.Failure(ex.Message);
            }
        }

        #endregion

        #region Package Operations

        /// <summary>
        /// 取得套件定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="packageName">套件名稱</param>
        /// <returns>套件定義</returns>
        public async Task<PackageDefinition> GetPackageDefinitionAsync(IDbConnection connection, string packageName)
        {
            try
            {
                _logger.LogInformation("正在取得套件定義: {PackageName}", packageName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(packageName))
                    throw new ArgumentException("套件名稱不能為空", nameof(packageName));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 取得套件規格和主體
                var spec = await GetObjectSourceAsync(connection, packageName, "PACKAGE");
                var body = await GetObjectSourceAsync(connection, packageName, "PACKAGE BODY");

                // 取得套件狀態
                var packageInfo = await GetPackageInfoAsync(connection, packageName);

                var packageDefinition = new PackageDefinition
                {
                    Name = packageName,
                    Owner = packageInfo.Item1,
                    Specification = spec,
                    Body = body,
                    SpecCreated = packageInfo.Item2,
                    BodyCreated = packageInfo.Item3,
                    SpecStatus = packageInfo.Item4,
                    BodyStatus = packageInfo.Item5
                };

                _logger.LogInformation("成功取得套件定義: {PackageName}", packageName);
                return packageDefinition;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得套件定義失敗: {PackageName}", packageName);
                throw new OracleManagementException($"取得套件定義失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 取得套件資訊
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="packageName">套件名稱</param>
        /// <returns>套件資訊</returns>
        private async Task<Tuple<string, DateTime, DateTime, string, string>> GetPackageInfoAsync(IDbConnection connection, string packageName)
        {
            try
            {
                var sql = @"
                    SELECT 
                        p.OWNER,
                        p.CREATED,
                        p.LAST_DDL_TIME,
                        p.STATUS,
                        pb.CREATED AS BODY_CREATED,
                        pb.LAST_DDL_TIME AS BODY_LAST_DDL_TIME,
                        pb.STATUS AS BODY_STATUS
                    FROM 
                        ALL_OBJECTS p
                    LEFT JOIN 
                        ALL_OBJECTS pb ON p.OBJECT_NAME = pb.OBJECT_NAME AND pb.OBJECT_TYPE = 'PACKAGE BODY'
                    WHERE 
                        p.OBJECT_NAME = :packageName
                        AND p.OBJECT_TYPE = 'PACKAGE'";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var parameter = command.CreateParameter();
                parameter.ParameterName = ":packageName";
                parameter.Value = packageName.ToUpper();
                command.Parameters.Add(parameter);

                using var reader = await Task.Run(() => command.ExecuteReader());
                if (await Task.Run(() => reader.Read()))
                {
                    var owner = reader["OWNER"].ToString();
                    var created = reader["CREATED"] as DateTime? ?? DateTime.MinValue;
                    var bodyCreated = reader["BODY_CREATED"] as DateTime? ?? DateTime.MinValue;
                    var status = reader["STATUS"].ToString();
                    var bodyStatus = reader["BODY_STATUS"]?.ToString() ?? "INVALID";

                    return new Tuple<string, DateTime, DateTime, string, string>(
                        owner, created, bodyCreated, status, bodyStatus);
                }
                else
                {
                    return new Tuple<string, DateTime, DateTime, string, string>(
                        string.Empty, DateTime.MinValue, DateTime.MinValue, "INVALID", "INVALID");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得套件資訊失敗: {PackageName}", packageName);
                throw new OracleManagementException($"取得套件資訊失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 編譯套件
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="packageName">套件名稱</param>
        /// <param name="spec">套件規格</param>
        /// <param name="body">套件主體</param>
        /// <returns>編譯結果</returns>
        public async Task<CompilationResult> CompilePackageAsync(IDbConnection connection, string packageName, string spec, string body)
        {
            try
            {
                _logger.LogInformation("正在編譯套件: {PackageName}", packageName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(packageName))
                    throw new ArgumentException("套件名稱不能為空", nameof(packageName));

                if (string.IsNullOrWhiteSpace(spec))
                    throw new ArgumentException("套件規格不能為空", nameof(spec));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 編譯套件規格
                try
                {
                    await _databaseRepository.ExecuteNonQueryAsync(connection, spec);
                    _logger.LogInformation("成功編譯套件規格: {PackageName}", packageName);

                    // 如果有套件主體，則編譯套件主體
                    if (!string.IsNullOrWhiteSpace(body))
                    {
                        try
                        {
                            await _databaseRepository.ExecuteNonQueryAsync(connection, body);
                            _logger.LogInformation("成功編譯套件主體: {PackageName}", packageName);
                            return CompilationResult.Success();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "編譯套件主體失敗: {PackageName}", packageName);
                            return ParseCompilationError(ex);
                        }
                    }

                    return CompilationResult.Success();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "編譯套件規格失敗: {PackageName}", packageName);
                    return ParseCompilationError(ex);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "編譯套件失敗: {PackageName}", packageName);
                return CompilationResult.Failure(ex.Message);
            }
        }

        #endregion

        #region Sequence Operations

        /// <summary>
        /// 取得序列定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="sequenceName">序列名稱</param>
        /// <returns>序列定義</returns>
        public async Task<SequenceDefinition> GetSequenceDefinitionAsync(IDbConnection connection, string sequenceName)
        {
            try
            {
                _logger.LogInformation("正在取得序列定義: {SequenceName}", sequenceName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(sequenceName))
                    throw new ArgumentException("序列名稱不能為空", nameof(sequenceName));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                var sql = @"
                    SELECT 
                        SEQUENCE_OWNER,
                        MIN_VALUE,
                        MAX_VALUE,
                        INCREMENT_BY,
                        CYCLE_FLAG,
                        ORDER_FLAG,
                        CACHE_SIZE,
                        LAST_NUMBER
                    FROM 
                        ALL_SEQUENCES
                    WHERE 
                        SEQUENCE_NAME = :sequenceName";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var parameter = command.CreateParameter();
                parameter.ParameterName = ":sequenceName";
                parameter.Value = sequenceName.ToUpper();
                command.Parameters.Add(parameter);

                using var reader = await Task.Run(() => command.ExecuteReader());
                if (await Task.Run(() => reader.Read()))
                {
                    var owner = reader["SEQUENCE_OWNER"].ToString();
                    var minValue = Convert.ToInt64(reader["MIN_VALUE"]);
                    var maxValue = Convert.ToInt64(reader["MAX_VALUE"]);
                    var incrementBy = Convert.ToInt64(reader["INCREMENT_BY"]);
                    var cycleFlag = reader["CYCLE_FLAG"].ToString() == "Y";
                    var orderFlag = reader["ORDER_FLAG"].ToString() == "Y";
                    var cacheSize = Convert.ToInt64(reader["CACHE_SIZE"]);
                    var lastNumber = Convert.ToInt64(reader["LAST_NUMBER"]);

                    var sequenceDefinition = new SequenceDefinition
                    {
                        Name = sequenceName,
                        Owner = owner,
                        MinValue = minValue,
                        MaxValue = maxValue,
                        IncrementBy = incrementBy,
                        IsCycling = cycleFlag,
                        IsOrdered = orderFlag,
                        CacheSize = cacheSize,
                        LastNumber = lastNumber
                    };

                    _logger.LogInformation("成功取得序列定義: {SequenceName}", sequenceName);
                    return sequenceDefinition;
                }
                else
                {
                    throw new OracleManagementException($"序列 {sequenceName} 不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得序列定義失敗: {SequenceName}", sequenceName);
                throw new OracleManagementException($"取得序列定義失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 儲存序列定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">序列定義</param>
        /// <returns>非同步工作</returns>
        public async Task SaveSequenceDefinitionAsync(IDbConnection connection, SequenceDefinition definition)
        {
            try
            {
                _logger.LogInformation("正在儲存序列定義: {SequenceName}", definition.Name);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (definition == null)
                    throw new ArgumentNullException(nameof(definition));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 驗證序列定義
                var validationResult = definition.Validate();
                if (!validationResult.IsValid)
                {
                    throw new ArgumentException($"序列定義無效: {string.Join(", ", validationResult.Errors)}");
                }

                // 檢查序列是否存在
                bool sequenceExists = await SequenceExistsAsync(connection, definition.Name);

                if (sequenceExists)
                {
                    // 更新現有序列
                    await UpdateSequenceAsync(connection, definition);
                }
                else
                {
                    // 創建新序列
                    await CreateSequenceAsync(connection, definition);
                }

                _logger.LogInformation("成功儲存序列定義: {SequenceName}", definition.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "儲存序列定義失敗: {SequenceName}", definition.Name);
                throw new OracleManagementException($"儲存序列定義失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 檢查序列是否存在
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="sequenceName">序列名稱</param>
        /// <returns>是否存在</returns>
        private async Task<bool> SequenceExistsAsync(IDbConnection connection, string sequenceName)
        {
            try
            {
                var sql = @"
                    SELECT COUNT(*) 
                    FROM ALL_SEQUENCES 
                    WHERE SEQUENCE_NAME = :sequenceName";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var parameter = command.CreateParameter();
                parameter.ParameterName = ":sequenceName";
                parameter.Value = sequenceName.ToUpper();
                command.Parameters.Add(parameter);

                var result = await Task.Run(() => command.ExecuteScalar());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檢查序列是否存在失敗: {SequenceName}", sequenceName);
                throw new OracleManagementException($"檢查序列是否存在失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 創建新序列
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">序列定義</param>
        /// <returns>非同步工作</returns>
        private async Task CreateSequenceAsync(IDbConnection connection, SequenceDefinition definition)
        {
            try
            {
                _logger.LogInformation("正在創建序列: {SequenceName}", definition.Name);

                // 產生 CREATE SEQUENCE 語句
                var sql = GenerateCreateSequenceSql(definition);

                // 執行 SQL
                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功創建序列: {SequenceName}", definition.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "創建序列失敗: {SequenceName}", definition.Name);
                throw new OracleManagementException($"創建序列失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新現有序列
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">序列定義</param>
        /// <returns>非同步工作</returns>
        private async Task UpdateSequenceAsync(IDbConnection connection, SequenceDefinition definition)
        {
            try
            {
                _logger.LogInformation("正在更新序列: {SequenceName}", definition.Name);

                // 產生 ALTER SEQUENCE 語句
                var sql = GenerateAlterSequenceSql(definition);

                // 執行 SQL
                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功更新序列: {SequenceName}", definition.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新序列失敗: {SequenceName}", definition.Name);
                throw new OracleManagementException($"更新序列失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 產生 CREATE SEQUENCE 語句
        /// </summary>
        /// <param name="definition">序列定義</param>
        /// <returns>SQL 語句</returns>
        private string GenerateCreateSequenceSql(SequenceDefinition definition)
        {
            var sql = new StringBuilder();

            sql.AppendLine($"CREATE SEQUENCE {definition.Name}");
            sql.AppendLine($"  INCREMENT BY {definition.IncrementBy}");
            sql.AppendLine($"  START WITH {definition.LastNumber}");
            sql.AppendLine($"  MINVALUE {definition.MinValue}");
            sql.AppendLine($"  MAXVALUE {definition.MaxValue}");
            sql.AppendLine($"  {(definition.IsCycling ? "CYCLE" : "NOCYCLE")}");
            sql.AppendLine($"  {(definition.IsOrdered ? "ORDER" : "NOORDER")}");
            sql.AppendLine($"  CACHE {definition.CacheSize}");

            return sql.ToString();
        }

        /// <summary>
        /// 產生 ALTER SEQUENCE 語句
        /// </summary>
        /// <param name="definition">序列定義</param>
        /// <returns>SQL 語句</returns>
        private string GenerateAlterSequenceSql(SequenceDefinition definition)
        {
            var sql = new StringBuilder();

            sql.AppendLine($"ALTER SEQUENCE {definition.Name}");
            sql.AppendLine($"  INCREMENT BY {definition.IncrementBy}");
            sql.AppendLine($"  MINVALUE {definition.MinValue}");
            sql.AppendLine($"  MAXVALUE {definition.MaxValue}");
            sql.AppendLine($"  {(definition.IsCycling ? "CYCLE" : "NOCYCLE")}");
            sql.AppendLine($"  {(definition.IsOrdered ? "ORDER" : "NOORDER")}");
            sql.AppendLine($"  CACHE {definition.CacheSize}");

            return sql.ToString();
        }

        #endregion

        #region Trigger Operations

        /// <summary>
        /// 取得觸發器定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="triggerName">觸發器名稱</param>
        /// <returns>觸發器定義</returns>
        public async Task<TriggerDefinition> GetTriggerDefinitionAsync(IDbConnection connection, string triggerName)
        {
            try
            {
                _logger.LogInformation("正在取得觸發器定義: {TriggerName}", triggerName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(triggerName))
                    throw new ArgumentException("觸發器名稱不能為空", nameof(triggerName));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 首先取得觸發器的擁有者
                var ownerSql = @"
                    SELECT OWNER
                    FROM ALL_TRIGGERS
                    WHERE TRIGGER_NAME = :triggerName";

                using var ownerCommand = connection.CreateCommand();
                ownerCommand.CommandText = ownerSql;
                ownerCommand.Parameters.Add(new OracleParameter(":triggerName", triggerName.ToUpper()));

                string? owner = null;
                using var ownerReader = await Task.Run(() => ownerCommand.ExecuteReader());
                if (await Task.Run(() => ownerReader.Read()))
                {
                    owner = ownerReader["OWNER"].ToString();
                }

                if (string.IsNullOrEmpty(owner))
                {
                    throw new OracleManagementException($"觸發器 {triggerName} 不存在");
                }

                // 使用 DBMS_METADATA.GET_DDL 取得完整的 DDL
                var ddlSql = "SELECT DBMS_METADATA.GET_DDL('TRIGGER', :triggerName, :owner) FROM DUAL";

                using var ddlCommand = connection.CreateCommand();
                ddlCommand.CommandText = ddlSql;
                ddlCommand.Parameters.Add(new OracleParameter(":triggerName", triggerName.ToUpper()));
                ddlCommand.Parameters.Add(new OracleParameter(":owner", owner.ToUpper()));

                var triggerDdl = string.Empty;
                using var ddlReader = await Task.Run(() => ddlCommand.ExecuteReader());
                if (await Task.Run(() => ddlReader.Read()))
                {
                    triggerDdl = ddlReader.IsDBNull(0) ? string.Empty : ddlReader.GetString(0);
                }

                // 取得觸發器的基本資訊
                var sql = @"
                    SELECT
                        t.OWNER,
                        t.TRIGGER_TYPE,
                        t.TRIGGERING_EVENT,
                        t.TABLE_NAME,
                        t.STATUS,
                        t.WHEN_CLAUSE
                    FROM
                        ALL_TRIGGERS t
                    WHERE
                        t.TRIGGER_NAME = :triggerName";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var parameter = command.CreateParameter();
                parameter.ParameterName = ":triggerName";
                parameter.Value = triggerName.ToUpper();
                command.Parameters.Add(parameter);

                using var reader = await Task.Run(() => command.ExecuteReader());
                if (await Task.Run(() => reader.Read()))
                {
                    var ownerFromQuery = reader["OWNER"].ToString();
                    var triggerType = reader["TRIGGER_TYPE"].ToString();
                    var triggeringEvent = reader["TRIGGERING_EVENT"].ToString();
                    var tableName = reader["TABLE_NAME"].ToString();
                    var status = reader["STATUS"].ToString();
                    var whenClause = reader["WHEN_CLAUSE"] as string;

                    var triggerDefinition = new TriggerDefinition
                    {
                        Name = triggerName,
                        Owner = ownerFromQuery,
                        TableName = tableName,
                        Type = ParseTriggerType(triggerType),
                        Event = ParseTriggerEvent(triggeringEvent),
                        Timing = ParseTriggerTiming(triggerType),
                        Body = triggerDdl, // 使用從 DBMS_METADATA.GET_DDL 取得的完整 DDL
                        IsEnabled = status == "ENABLED",
                        Status = status,
                        Condition = whenClause ?? string.Empty
                    };

                    _logger.LogInformation("成功取得觸發器定義: {TriggerName}", triggerName);
                    return triggerDefinition;
                }
                else
                {
                    throw new OracleManagementException($"觸發器 {triggerName} 不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得觸發器定義失敗: {TriggerName}", triggerName);
                throw new OracleManagementException($"取得觸發器定義失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 儲存觸發器定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">觸發器定義</param>
        /// <returns>編譯結果</returns>
        public async Task<CompilationResult> SaveTriggerDefinitionAsync(IDbConnection connection, TriggerDefinition definition)
        {
            try
            {
                _logger.LogInformation("正在儲存觸發器定義: {TriggerName}", definition.Name);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (definition == null)
                    throw new ArgumentNullException(nameof(definition));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 驗證觸發器定義
                var validationResult = definition.Validate();
                if (!validationResult.IsValid)
                {
                    return CompilationResult.Failure($"觸發器定義無效: {string.Join(", ", validationResult.Errors)}");
                }

                // 檢查觸發器是否存在
                bool triggerExists = await TriggerExistsAsync(connection, definition.Name);

                if (triggerExists)
                {
                    // 刪除現有觸發器
                    await DropTriggerAsync(connection, definition.Name);
                }

                // 產生 CREATE TRIGGER 語句
                var sql = GenerateCreateTriggerSql(definition);

                // 執行 SQL
                try
                {
                    await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                    // 如果需要禁用觸發器
                    if (!definition.IsEnabled)
                    {
                        await UpdateTriggerStatusAsync(connection, definition);
                    }

                    _logger.LogInformation("成功儲存觸發器定義: {TriggerName}", definition.Name);
                    return CompilationResult.Success();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "儲存觸發器定義失敗: {TriggerName}", definition.Name);
                    return ParseCompilationError(ex);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "儲存觸發器定義失敗: {TriggerName}", definition.Name);
                return CompilationResult.Failure(ex.Message);
            }
        }

        /// <summary>
        /// 檢查觸發器是否存在
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="triggerName">觸發器名稱</param>
        /// <returns>是否存在</returns>
        private async Task<bool> TriggerExistsAsync(IDbConnection connection, string triggerName)
        {
            try
            {
                var sql = @"
                    SELECT COUNT(*) 
                    FROM ALL_TRIGGERS 
                    WHERE TRIGGER_NAME = :triggerName";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var parameter = command.CreateParameter();
                parameter.ParameterName = ":triggerName";
                parameter.Value = triggerName.ToUpper();
                command.Parameters.Add(parameter);

                var result = await Task.Run(() => command.ExecuteScalar());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檢查觸發器是否存在失敗: {TriggerName}", triggerName);
                throw new OracleManagementException($"檢查觸發器是否存在失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 產生 CREATE TRIGGER 語句
        /// </summary>
        /// <param name="definition">觸發器定義</param>
        /// <returns>SQL 語句</returns>
        private string GenerateCreateTriggerSql(TriggerDefinition definition)
        {
            var sql = new StringBuilder();

            sql.AppendLine($"CREATE OR REPLACE TRIGGER {definition.Name}");

            // 觸發時機
            switch (definition.Timing)
            {
                case TriggerTiming.Before:
                    sql.Append("BEFORE ");
                    break;
                case TriggerTiming.After:
                    sql.Append("AFTER ");
                    break;
                case TriggerTiming.Instead:
                    sql.Append("INSTEAD OF ");
                    break;
            }

            // 觸發事件
            switch (definition.Event)
            {
                case TriggerEvent.Insert:
                    sql.Append("INSERT");
                    break;
                case TriggerEvent.Update:
                    sql.Append("UPDATE");
                    break;
                case TriggerEvent.Delete:
                    sql.Append("DELETE");
                    break;
                case TriggerEvent.InsertOrUpdate:
                    sql.Append("INSERT OR UPDATE");
                    break;
                case TriggerEvent.InsertOrDelete:
                    sql.Append("INSERT OR DELETE");
                    break;
                case TriggerEvent.UpdateOrDelete:
                    sql.Append("UPDATE OR DELETE");
                    break;
                case TriggerEvent.InsertOrUpdateOrDelete:
                    sql.Append("INSERT OR UPDATE OR DELETE");
                    break;
            }

            sql.AppendLine($" ON {definition.TableName}");

            // 觸發類型
            if (definition.Type == TriggerType.Row)
            {
                sql.AppendLine("FOR EACH ROW");
            }

            // 觸發條件
            if (!string.IsNullOrWhiteSpace(definition.Condition))
            {
                sql.AppendLine($"WHEN ({definition.Condition})");
            }

            // 觸發器主體
            sql.AppendLine("BEGIN");
            sql.AppendLine(definition.Body);
            sql.AppendLine("END;");

            return sql.ToString();
        }

        #endregion

        #region Index Operations

        /// <summary>
        /// 取得索引定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="indexName">索引名稱</param>
        /// <returns>索引定義</returns>
        public async Task<IndexDefinition> GetIndexDefinitionAsync(IDbConnection connection, string indexName)
        {
            return await GetIndexDefinitionAsync(connection, indexName, null);
        }

        /// <summary>
        /// 取得索引定義
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="indexName">索引名稱</param>
        /// <param name="schemaName">Schema名稱（可選）</param>
        /// <returns>索引定義</returns>
        public async Task<IndexDefinition> GetIndexDefinitionAsync(IDbConnection connection, string indexName, string? schemaName)
        {
            try
            {
                _logger.LogInformation("正在取得索引定義: {IndexName}, Schema: {Schema}", indexName, schemaName ?? "未指定");

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(indexName))
                    throw new ArgumentException("索引名稱不能為空", nameof(indexName));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 根據是否有指定 Schema 來建構 SQL 查詢
                var sql = string.IsNullOrEmpty(schemaName)
                    ? @"
                        SELECT
                            i.INDEX_NAME,
                            i.INDEX_TYPE,
                            i.TABLE_NAME,
                            i.TABLE_OWNER,
                            i.UNIQUENESS,
                            i.STATUS,
                            i.TABLESPACE_NAME,
                            i.LAST_ANALYZED,
                            i.DISTINCT_KEYS,
                            i.LEAF_BLOCKS,
                            i.CLUSTERING_FACTOR
                        FROM
                            ALL_INDEXES i
                        WHERE
                            i.INDEX_NAME = :indexName
                            AND i.TABLE_OWNER = USER"
                    : @"
                        SELECT
                            i.INDEX_NAME,
                            i.INDEX_TYPE,
                            i.TABLE_NAME,
                            i.TABLE_OWNER,
                            i.UNIQUENESS,
                            i.STATUS,
                            i.TABLESPACE_NAME,
                            i.LAST_ANALYZED,
                            i.DISTINCT_KEYS,
                            i.LEAF_BLOCKS,
                            i.CLUSTERING_FACTOR
                        FROM
                            ALL_INDEXES i
                        WHERE
                            i.INDEX_NAME = :indexName
                            AND i.TABLE_OWNER = :schemaName";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var indexParameter = command.CreateParameter();
                indexParameter.ParameterName = ":indexName";
                indexParameter.Value = indexName;
                command.Parameters.Add(indexParameter);

                // 如果有指定 Schema，添加 Schema 參數
                if (!string.IsNullOrEmpty(schemaName))
                {
                    var schemaParameter = command.CreateParameter();
                    schemaParameter.ParameterName = ":schemaName";
                    schemaParameter.Value = schemaName.ToUpper();
                    command.Parameters.Add(schemaParameter);
                }

                using var reader = await Task.Run(() => command.ExecuteReader());
                if (await Task.Run(() => reader.Read()))
                {
                    var indexType = reader["INDEX_TYPE"].ToString();
                    var tableName = reader["TABLE_NAME"].ToString();
                    var tableOwner = reader["TABLE_OWNER"].ToString();
                    var uniqueness = reader["UNIQUENESS"].ToString();
                    var status = reader["STATUS"].ToString();
                    var tablespace = reader["TABLESPACE_NAME"].ToString();
                    var lastAnalyzed = reader["LAST_ANALYZED"] as DateTime? ?? DateTime.MinValue;
                    var distinctKeys = Convert.ToInt32(reader["DISTINCT_KEYS"]);
                    var leafBlocks = Convert.ToInt32(reader["LEAF_BLOCKS"]);
                    var clustering = Convert.ToInt32(reader["CLUSTERING_FACTOR"]);

                    var index = new IndexDefinition
                    {
                        Name = indexName,
                        TableName = tableName,
                        Owner = tableOwner,
                        Type = ParseIndexType(indexType),
                        IsUnique = uniqueness == "UNIQUE",
                        Status = status,
                        Tablespace = tablespace,
                        LastAnalyzed = lastAnalyzed,
                        DistinctKeys = distinctKeys,
                        LeafBlocks = leafBlocks,
                        Clustering = clustering
                    };

                    // 取得索引欄位
                    index.Columns = await GetIndexColumnsAsync(connection, indexName, schemaName);

                    _logger.LogInformation("成功取得索引定義: {IndexName}", indexName);
                    return index;
                }
                else
                {
                    throw new OracleManagementException($"索引 {indexName} 不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取得索引定義失敗: {IndexName}", indexName);
                throw new OracleManagementException($"取得索引定義失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 重建索引
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="indexName">索引名稱</param>
        /// <returns>非同步工作</returns>
        public async Task RebuildIndexAsync(IDbConnection connection, string indexName)
        {
            try
            {
                _logger.LogInformation("正在重建索引: {IndexName}", indexName);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (string.IsNullOrWhiteSpace(indexName))
                    throw new ArgumentException("索引名稱不能為空", nameof(indexName));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                var sql = $"ALTER INDEX {indexName} REBUILD";

                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功重建索引: {IndexName}", indexName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重建索引失敗: {IndexName}", indexName);
                throw new OracleManagementException($"重建索引失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 創建索引
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">索引定義</param>
        /// <returns>非同步工作</returns>
        public async Task CreateIndexAsync(IDbConnection connection, IndexDefinition definition)
        {
            try
            {
                _logger.LogInformation("正在創建索引: {IndexName}", definition.Name);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (definition == null)
                    throw new ArgumentNullException(nameof(definition));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 驗證索引定義
                var validationResult = definition.Validate();
                if (!validationResult.IsValid)
                {
                    throw new ArgumentException($"索引定義無效: {string.Join(", ", validationResult.Errors)}");
                }

                // 增強的索引名稱重複檢查
                await ValidateIndexCreationAsync(connection, definition);

                // 產生 CREATE INDEX 語句
                var sql = GenerateCreateIndexSql(definition);

                // 執行 SQL 並實作錯誤處理和回滾機制
                await ExecuteIndexCreationWithRollbackAsync(connection, definition, sql);

                _logger.LogInformation("成功創建索引: {IndexName}", definition.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "創建索引失敗: {IndexName}", definition.Name);
                throw new OracleManagementException($"創建索引失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新索引
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">索引定義</param>
        /// <returns>非同步工作</returns>
        public async Task UpdateIndexAsync(IDbConnection connection, IndexDefinition definition)
        {
            try
            {
                _logger.LogInformation("正在更新索引: {IndexName}", definition.Name);

                if (connection == null)
                    throw new ArgumentNullException(nameof(connection));

                if (definition == null)
                    throw new ArgumentNullException(nameof(definition));

                if (connection.State != ConnectionState.Open)
                    throw new InvalidOperationException("資料庫連線未開啟");

                // 驗證索引定義
                var validationResult = definition.Validate();
                if (!validationResult.IsValid)
                {
                    throw new ArgumentException($"索引定義無效: {string.Join(", ", validationResult.Errors)}");
                }

                // 檢查索引是否存在
                var indexExists = await IndexExistsAsync(connection, definition.Name, definition.Owner);
                if (!indexExists)
                {
                    throw new OracleManagementException($"索引 {definition.Owner}.{definition.Name} 不存在");
                }

                // 對於索引更新，我們需要先刪除舊索引，再創建新索引
                // 因為 Oracle 不支援直接修改索引結構
                await DropIndexAsync(connection, definition.Name, definition.Owner);
                
                // 產生 CREATE INDEX 語句
                var sql = GenerateCreateIndexSql(definition);

                // 執行 SQL
                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);

                _logger.LogInformation("成功更新索引: {IndexName}", definition.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新索引失敗: {IndexName}", definition.Name);
                throw new OracleManagementException($"更新索引失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 檢查索引是否存在
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="indexName">索引名稱</param>
        /// <param name="owner">擁有者</param>
        /// <returns>是否存在</returns>
        private async Task<bool> IndexExistsAsync(IDbConnection connection, string indexName, string owner)
        {
            try
            {
                var sql = @"
                    SELECT COUNT(*) 
                    FROM ALL_INDEXES 
                    WHERE INDEX_NAME = :indexName AND OWNER = :owner";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var indexParam = command.CreateParameter();
                indexParam.ParameterName = ":indexName";
                indexParam.Value = indexName;
                command.Parameters.Add(indexParam);

                var ownerParam = command.CreateParameter();
                ownerParam.ParameterName = ":owner";
                ownerParam.Value = owner.ToUpper();
                command.Parameters.Add(ownerParam);

                var result = await Task.Run(() => command.ExecuteScalar());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檢查索引是否存在失敗: {IndexName}", indexName);
                throw new OracleManagementException($"檢查索引是否存在失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 刪除索引
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="indexName">索引名稱</param>
        /// <param name="owner">擁有者</param>
        /// <returns>非同步工作</returns>
        private async Task DropIndexAsync(IDbConnection connection, string indexName, string owner)
        {
            try
            {
                var sql = $"DROP INDEX {owner}.{indexName}";
                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除索引失敗: {IndexName}", indexName);
                throw new OracleManagementException($"刪除索引失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 驗證索引創建的前置條件
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">索引定義</param>
        /// <returns>非同步工作</returns>
        private async Task ValidateIndexCreationAsync(IDbConnection connection, IndexDefinition definition)
        {
            // 檢查索引名稱是否已存在
            var indexExists = await IndexExistsAsync(connection, definition.Name, definition.Owner);
            if (indexExists)
            {
                throw new OracleManagementException($"索引 {definition.Owner}.{definition.Name} 已存在");
            }

            // 檢查表格是否存在
            var tableExists = await TableExistsAsync(connection, definition.TableName);
            if (!tableExists)
            {
                throw new OracleManagementException($"資料表 {definition.TableName} 不存在");
            }

            // 檢查索引欄位是否存在於表格中
            await ValidateIndexColumnsExistAsync(connection, definition);

            // 檢查是否存在相同欄位組合的索引
            await ValidateDuplicateIndexColumnsAsync(connection, definition);
        }

        /// <summary>
        /// 驗證索引欄位是否存在於表格中
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">索引定義</param>
        /// <returns>非同步工作</returns>
        private async Task ValidateIndexColumnsExistAsync(IDbConnection connection, IndexDefinition definition)
        {
            try
            {
                var sql = @"
                    SELECT COLUMN_NAME 
                    FROM ALL_TAB_COLUMNS 
                    WHERE TABLE_NAME = :tableName 
                    AND OWNER = :owner";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var tableParam = command.CreateParameter();
                tableParam.ParameterName = ":tableName";
                tableParam.Value = definition.TableName.ToUpper();
                command.Parameters.Add(tableParam);

                var ownerParam = command.CreateParameter();
                ownerParam.ParameterName = ":owner";
                ownerParam.Value = definition.Owner.ToUpper();
                command.Parameters.Add(ownerParam);

                var existingColumns = new HashSet<string>();
                using var reader = await Task.Run(() => command.ExecuteReader());
                while (await Task.Run(() => reader.Read()))
                {
                    existingColumns.Add(reader["COLUMN_NAME"].ToString().ToUpper());
                }

                var invalidColumns = definition.Columns
                    .Where(c => !existingColumns.Contains(c.ColumnName.ToUpper()))
                    .Select(c => c.ColumnName)
                    .ToList();

                if (invalidColumns.Any())
                {
                    throw new OracleManagementException($"以下欄位不存在於表格 {definition.TableName} 中: {string.Join(", ", invalidColumns)}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "驗證索引欄位失敗: {TableName}", definition.TableName);
                throw new OracleManagementException($"驗證索引欄位失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 檢查是否存在相同欄位組合的索引
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">索引定義</param>
        /// <returns>非同步工作</returns>
        private async Task ValidateDuplicateIndexColumnsAsync(IDbConnection connection, IndexDefinition definition)
        {
            try
            {
                var sql = @"
                    SELECT i.INDEX_NAME, 
                           LISTAGG(ic.COLUMN_NAME, ',') WITHIN GROUP (ORDER BY ic.COLUMN_POSITION) as COLUMN_LIST
                    FROM ALL_INDEXES i
                    JOIN ALL_IND_COLUMNS ic ON i.INDEX_NAME = ic.INDEX_NAME AND i.OWNER = ic.INDEX_OWNER
                    WHERE i.TABLE_NAME = :tableName 
                    AND i.OWNER = :owner
                    GROUP BY i.INDEX_NAME";

                using var command = connection.CreateCommand();
                command.CommandText = sql;

                var tableParam = command.CreateParameter();
                tableParam.ParameterName = ":tableName";
                tableParam.Value = definition.TableName.ToUpper();
                command.Parameters.Add(tableParam);

                var ownerParam = command.CreateParameter();
                ownerParam.ParameterName = ":owner";
                ownerParam.Value = definition.Owner.ToUpper();
                command.Parameters.Add(ownerParam);

                var newIndexColumns = string.Join(",", definition.Columns.OrderBy(c => c.Position).Select(c => c.ColumnName.ToUpper()));

                using var reader = await Task.Run(() => command.ExecuteReader());
                while (await Task.Run(() => reader.Read()))
                {
                    var existingIndexName = reader["INDEX_NAME"].ToString();
                    var existingColumns = reader["COLUMN_LIST"].ToString();

                    if (existingColumns.Equals(newIndexColumns, StringComparison.OrdinalIgnoreCase))
                    {
                        throw new OracleManagementException($"已存在相同欄位組合的索引: {existingIndexName}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檢查重複索引欄位組合失敗: {TableName}", definition.TableName);
                throw new OracleManagementException($"檢查重複索引欄位組合失敗: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 執行索引創建並實作錯誤處理和回滾機制
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="definition">索引定義</param>
        /// <param name="sql">CREATE INDEX SQL 語句</param>
        /// <returns>非同步工作</returns>
        private async Task ExecuteIndexCreationWithRollbackAsync(IDbConnection connection, IndexDefinition definition, string sql)
        {
            bool indexCreated = false;
            
            try
            {
                _logger.LogInformation("執行索引創建 SQL: {Sql}", sql);
                
                // 執行 CREATE INDEX 語句
                await _databaseRepository.ExecuteNonQueryAsync(connection, sql);
                indexCreated = true;

                // 驗證索引是否成功創建
                var indexExists = await IndexExistsAsync(connection, definition.Name, definition.Owner);
                if (!indexExists)
                {
                    throw new OracleManagementException("索引創建後驗證失敗，索引可能未成功創建");
                }

                _logger.LogInformation("索引創建驗證成功: {IndexName}", definition.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "索引創建失敗，開始回滾: {IndexName}", definition.Name);
                
                // 如果索引已創建但後續驗證失敗，嘗試清理
                if (indexCreated)
                {
                    try
                    {
                        await CleanupFailedIndexAsync(connection, definition.Name, definition.Owner);
                    }
                    catch (Exception cleanupEx)
                    {
                        _logger.LogError(cleanupEx, "索引回滾清理失敗: {IndexName}", definition.Name);
                        // 不拋出清理異常，保留原始異常
                    }
                }

                // 重新拋出原始異常
                throw;
            }
        }

        /// <summary>
        /// 清理失敗的索引創建
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <param name="indexName">索引名稱</param>
        /// <param name="owner">擁有者</param>
        /// <returns>非同步工作</returns>
        private async Task CleanupFailedIndexAsync(IDbConnection connection, string indexName, string owner)
        {
            try
            {
                var indexExists = await IndexExistsAsync(connection, indexName, owner);
                if (indexExists)
                {
                    _logger.LogInformation("清理失敗的索引: {IndexName}", indexName);
                    await DropIndexAsync(connection, indexName, owner);
                    _logger.LogInformation("成功清理失敗的索引: {IndexName}", indexName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理失敗的索引時發生錯誤: {IndexName}", indexName);
                throw;
            }
        }



        #endregion





        #region Helper Methods

        /// <summary>
        /// 解析編譯錯誤
        /// </summary>
        /// <param name="ex">例外</param>
        /// <returns>編譯結果</returns>
        private CompilationResult ParseCompilationError(Exception ex)
        {
            if (ex is OracleException oracleEx)
            {
                // 嘗試解析 Oracle 錯誤訊息中的行號和位置
                var errorMessage = oracleEx.Message;
                var errorCode = oracleEx.Number.ToString();

                // 嘗試從錯誤訊息中提取行號和位置
                int? errorLine = null;
                int? errorPosition = null;

                // 常見的 Oracle 錯誤訊息格式：ORA-XXXXX: ... line XX, column YY
                var lineMatch = System.Text.RegularExpressions.Regex.Match(errorMessage, @"line\s+(\d+)");
                if (lineMatch.Success)
                {
                    errorLine = int.Parse(lineMatch.Groups[1].Value);
                }

                var posMatch = System.Text.RegularExpressions.Regex.Match(errorMessage, @"column\s+(\d+)");
                if (posMatch.Success)
                {
                    errorPosition = int.Parse(posMatch.Groups[1].Value);
                }

                return CompilationResult.Failure(errorMessage, errorCode, errorLine, errorPosition);
            }
            else
            {
                return CompilationResult.Failure(ex.Message);
            }
        }

        #endregion
    }
}