using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.Exceptions;

namespace OracleMS.ViewModels
{
    /// <summary>
    /// 索引編輯器 ViewModel
    /// </summary>
    public class IndexEditorViewModel : BaseObjectEditorViewModel
    {
        #region Events

        /// <summary>
        /// 索引創建成功事件
        /// </summary>
        public event EventHandler<IndexCreatedEventArgs>? IndexCreatedSuccessfully;

        #endregion

        #region Fields

        private readonly IndexEditorInfo _indexInfo;
        private readonly ColumnSelectionManager _columnSelectionManager;
        private IndexDefinition _indexDefinition;
        private string _ddlPreview = string.Empty;
        private bool _isOperationInProgress;
        private bool _isLoadingSchemas;
        private bool _isLoadingTables;
        private bool _isLoadingColumns;
        private string _loadingMessage = string.Empty;
        private double _loadingProgress;

        // 即時驗證相關屬性
        private ValidationResult _currentValidationResult = new ValidationResult();
        private bool _isRealTimeValidationEnabled = true;
        private System.Timers.Timer? _validationTimer;

        // 效能監控相關屬性
        private System.Timers.Timer? _performanceMonitorTimer;

        // 效能優化相關屬性
        private readonly SemaphoreSlim _loadingSemaphore = new SemaphoreSlim(1, 1);
        private readonly Dictionary<string, List<string>> _tableColumnsCache = new Dictionary<string, List<string>>();
        private readonly Dictionary<string, List<string>> _schemaTablesCache = new Dictionary<string, List<string>>();
        private DateTime _lastCacheUpdate = DateTime.MinValue;
        private readonly PerformanceSettings _performanceSettings = PerformanceSettings.Default;

        // 取消令牌用於取消長時間運行的操作
        private CancellationTokenSource? _loadingCancellationTokenSource;

        // 雙模式操作相關屬性
        private bool _isCreateMode;
        private bool _isEditMode;
        private string _selectedSchema = string.Empty;
        private string _selectedTable = string.Empty;

        #endregion

        #region Properties

        /// <summary>
        /// 索引定義
        /// </summary>
        public IndexDefinition IndexDefinition
        {
            get => _indexDefinition;
            private set
            {
                if (SetProperty(ref _indexDefinition, value))
                {
                    NotifyCanExecuteChanged();

                    // 當 IndexDefinition 被設定時，如果是編輯模式且有欄位資料，嘗試恢復索引欄位
                    if (IsEditMode && value?.Columns?.Any() == true)
                    {
                        _logger.LogInformation("IndexDefinition 已設定，欄位數量: {Count}，可用欄位數量: {AvailableCount}",
                            value.Columns.Count, _columnSelectionManager.AvailableColumns.Count);

                        // 如果可用欄位已經載入，立即恢復索引欄位
                        if (_columnSelectionManager.AvailableColumns.Any())
                        {
                            _logger.LogInformation("可用欄位已載入，立即恢復索引欄位");
                            RestoreIndexColumnsToSelected();
                        }
                        else
                        {
                            _logger.LogInformation("可用欄位尚未載入，將在欄位載入完成後恢復");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// DDL 預覽
        /// </summary>
        public string DdlPreview
        {
            get => _ddlPreview;
            private set => SetProperty(ref _ddlPreview, value);
        }

        /// <summary>
        /// 是否有操作正在進行中
        /// </summary>
        public bool IsOperationInProgress
        {
            get => _isOperationInProgress;
            private set => SetProperty(ref _isOperationInProgress, value);
        }

        /// <summary>
        /// 是否正在載入Schema
        /// </summary>
        public bool IsLoadingSchemas
        {
            get => _isLoadingSchemas;
            private set => SetProperty(ref _isLoadingSchemas, value);
        }

        /// <summary>
        /// 是否正在載入Table
        /// </summary>
        public bool IsLoadingTables
        {
            get => _isLoadingTables;
            private set => SetProperty(ref _isLoadingTables, value);
        }

        /// <summary>
        /// 是否正在載入欄位
        /// </summary>
        public bool IsLoadingColumns
        {
            get => _isLoadingColumns;
            private set => SetProperty(ref _isLoadingColumns, value);
        }

        /// <summary>
        /// 載入訊息
        /// </summary>
        public string LoadingMessage
        {
            get => _loadingMessage;
            private set => SetProperty(ref _loadingMessage, value);
        }

        /// <summary>
        /// 載入進度 (0-100)
        /// </summary>
        public double LoadingProgress
        {
            get => _loadingProgress;
            private set => SetProperty(ref _loadingProgress, value);
        }

        /// <summary>
        /// 是否有任何載入操作正在進行
        /// </summary>
        public bool IsAnyLoadingInProgress => IsLoadingSchemas || IsLoadingTables || IsLoadingColumns || IsOperationInProgress;

        /// <summary>
        /// 快取的Schema數量
        /// </summary>
        public int CachedSchemasCount => _schemaTablesCache.Count(kvp => kvp.Key.StartsWith("tables_"));

        /// <summary>
        /// 快取的Table數量
        /// </summary>
        public int CachedTablesCount => _tableColumnsCache.Count;

        /// <summary>
        /// 記憶體使用情況文字
        /// </summary>
        public static string MemoryUsageText
        {
            get
            {
                var memoryMB = GetMemoryUsage() / 1024 / 1024;
                return $"記憶體: {memoryMB}MB";
            }
        }

        /// <summary>
        /// 效能設定
        /// </summary>
        public PerformanceSettings PerformanceSettings => _performanceSettings;

        /// <summary>
        /// 是否為創建模式
        /// </summary>
        public bool IsCreateMode
        {
            get => _isCreateMode;
            private set => SetProperty(ref _isCreateMode, value);
        }

        /// <summary>
        /// 是否為編輯模式
        /// </summary>
        public bool IsEditMode
        {
            get => _isEditMode;
            private set => SetProperty(ref _isEditMode, value);
        }

        /// <summary>
        /// 當前驗證結果
        /// </summary>
        public ValidationResult CurrentValidationResult
        {
            get => _currentValidationResult;
            private set => SetProperty(ref _currentValidationResult, value);
        }

        /// <summary>
        /// 是否啟用即時驗證
        /// </summary>
        public bool IsRealTimeValidationEnabled
        {
            get => _isRealTimeValidationEnabled;
            set
            {
                if (SetProperty(ref _isRealTimeValidationEnabled, value))
                {
                    if (value)
                    {
                        TriggerRealTimeValidation();
                    }
                    else
                    {
                        _validationTimer?.Stop();
                    }
                }
            }
        }

        /// <summary>
        /// 是否有驗證錯誤
        /// </summary>
        public bool HasValidationErrors => !CurrentValidationResult.IsValid;

        /// <summary>
        /// 驗證錯誤訊息（格式化為單一字串）
        /// </summary>
        public string ValidationErrorMessage => string.Join("\n", CurrentValidationResult.Errors);

        /// <summary>
        /// 是否有驗證警告
        /// </summary>
        public bool HasValidationWarnings => CurrentValidationResult.HasWarnings;

        /// <summary>
        /// 驗證警告訊息（格式化為單一字串）
        /// </summary>
        public string ValidationWarningMessage => string.Join("\n", CurrentValidationResult.Warnings.Select(w => w.Message));

        /// <summary>
        /// 驗證摘要訊息
        /// </summary>
        public string ValidationSummary => CurrentValidationResult.GetErrorSummary();

        /// <summary>
        /// 錯誤數量
        /// </summary>
        public int ErrorCount => CurrentValidationResult.ErrorCount;

        /// <summary>
        /// 警告數量
        /// </summary>
        public int WarningCount => CurrentValidationResult.WarningCount;

        /// <summary>
        /// 驗證狀態文字
        /// </summary>
        public string ValidationStatusText
        {
            get
            {
                if (CurrentValidationResult.IsValid && !CurrentValidationResult.HasWarnings)
                    return IndexValidationMessages.ValidationCompleted;
                
                if (!CurrentValidationResult.IsValid)
                    return $"{IndexValidationMessages.ValidationFailed} ({ErrorCount} 個錯誤)";
                
                if (CurrentValidationResult.HasWarnings)
                    return $"驗證通過但有 {WarningCount} 個警告";
                
                return IndexValidationMessages.ValidationInProgress;
            }
        }

        /// <summary>
        /// 選定的Schema
        /// </summary>
        public string SelectedSchema
        {
            get => _selectedSchema;
            set
            {
                if (SetProperty(ref _selectedSchema, value))
                {
                    // 當schema變更時，自動載入對應的tables
                    _ = LoadTablesForSchemaAsync(value);
                    NotifyCanExecuteChanged();
                }
            }
        }

        /// <summary>
        /// 選定的Table
        /// </summary>
        public string SelectedTable
        {
            get => _selectedTable;
            set
            {
                _logger.LogInformation("SelectedTable 設定，舊值: {OldValue}, 新值: {NewValue}", _selectedTable, value);
                if (SetProperty(ref _selectedTable, value))
                {
                    _logger.LogInformation("SelectedTable 已變更為: {TableName}，開始載入欄位", value);
                    // 當table變更時，載入欄位資訊
                    // 但在初始化期間避免重複載入
                    if (!_isInitializing)
                    {
                        _ = LoadColumnsForTableAsync(value);
                    }
                    NotifyCanExecuteChanged();
                }
                else
                {
                    _logger.LogDebug("SelectedTable 值未變更: {TableName}", value);
                }
            }
        }

        /// <summary>
        /// 可用的Schema集合
        /// </summary>
        public ObservableCollection<string> AvailableSchemas { get; }

        /// <summary>
        /// 可用的Table集合
        /// </summary>
        public ObservableCollection<string> AvailableTables { get; }

        /// <summary>
        /// 可用欄位集合（左側ListBox）
        /// </summary>
        public ObservableCollection<string> AvailableColumns => _columnSelectionManager.AvailableColumns;

        /// <summary>
        /// 已選欄位集合（右側ListBox）
        /// </summary>
        public ObservableCollection<string> SelectedColumns => _columnSelectionManager.SelectedColumns;

        /// <summary>
        /// 重建索引命令
        /// </summary>
        public ICommand RebuildIndexCommand { get; }

        /// <summary>
        /// 分析索引命令
        /// </summary>
        public ICommand AnalyzeIndexCommand { get; }

        /// <summary>
        /// 複製 DDL 命令
        /// </summary>
        public ICommand CopyDdlCommand { get; }

        /// <summary>
        /// 儲存 DDL 命令
        /// </summary>
        public ICommand SaveDdlCommand { get; }

        /// <summary>
        /// 新增欄位命令（將欄位從左側移到右側）
        /// </summary>
        public ICommand AddColumnCommand { get; }

        /// <summary>
        /// 移除欄位命令（將欄位從右側移回左側）
        /// </summary>
        public ICommand RemoveColumnCommand { get; }

        /// <summary>
        /// 欄位上移命令
        /// </summary>
        public ICommand MoveColumnUpCommand { get; }

        /// <summary>
        /// 欄位下移命令
        /// </summary>
        public ICommand MoveColumnDownCommand { get; }

        /// <summary>
        /// 創建索引命令
        /// </summary>
        public ICommand CreateIndexCommand { get; }

        /// <summary>
        /// 更新索引命令
        /// </summary>
        public ICommand UpdateIndexCommand { get; }

        /// <summary>
        /// 取消載入命令
        /// </summary>
        public ICommand CancelLoadingCommand { get; }

        #endregion

        #region Constructor

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="indexInfo">索引編輯器參數</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        /// <param name="logger">日誌記錄器</param>
        public IndexEditorViewModel(
            IndexEditorInfo indexInfo,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
            : base(indexInfo?.IndexName ?? "新索引", DatabaseObjectType.Index, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger)
        {
            _indexInfo = indexInfo ?? throw new ArgumentNullException(nameof(indexInfo));
            _columnSelectionManager = new ColumnSelectionManager();

            // 初始化集合
            AvailableSchemas = new ObservableCollection<string>();
            AvailableTables = new ObservableCollection<string>();

            // 設定模式
            IsEditMode = _indexInfo.IsEditMode;
            IsCreateMode = !_indexInfo.IsEditMode;

            // 初始化索引定義
            _indexDefinition = new IndexDefinition 
            { 
                Name = _indexInfo.IndexName ?? string.Empty,
                Owner = _indexInfo.Schema ?? string.Empty,
                TableName = _indexInfo.TableName ?? string.Empty,
                IsUnique = _indexInfo.IsUnique
            };

            // 設定初始選擇 - 使用公開屬性以觸發相關事件
            // 注意：這些會在 InitializeAsync 中正確設定，這裡只是預設值

            // 初始化命令
            RebuildIndexCommand = new AsyncRelayCommand(RebuildIndexAsync, CanRebuildIndex);
            AnalyzeIndexCommand = new AsyncRelayCommand(AnalyzeIndexAsync, CanAnalyzeIndex);
            CopyDdlCommand = new RelayCommand(CopyDdlToClipboard);
            SaveDdlCommand = new RelayCommand(SaveDdlToFile);

            // 初始化欄位選擇命令
            AddColumnCommand = new RelayCommand<string>(AddColumn, CanAddColumn);
            RemoveColumnCommand = new RelayCommand<string>(RemoveColumn, CanRemoveColumn);
            MoveColumnUpCommand = new RelayCommand<string>(MoveColumnUp, CanMoveColumnUp);
            MoveColumnDownCommand = new RelayCommand<string>(MoveColumnDown, CanMoveColumnDown);

            // 初始化索引操作命令
            CreateIndexCommand = new AsyncRelayCommand(CreateIndexAsync, CanCreateIndex);
            UpdateIndexCommand = new AsyncRelayCommand(UpdateIndexAsync, CanUpdateIndex);
            CancelLoadingCommand = new RelayCommand(CancelLoadingOperations, () => IsAnyLoadingInProgress);

            // 初始化即時驗證計時器
            InitializeRealTimeValidation();

            // 初始化效能監控計時器
            InitializePerformanceMonitoring();

            // 初始化取消令牌
            _loadingCancellationTokenSource = new CancellationTokenSource();

            // 啟動背景預載入常用資料（提升使用者體驗）
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(1000); // 延遲1秒，讓主要初始化先完成
                    await PreloadCommonDataAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "背景預載入常用資料時發生錯誤");
                }
            });
        }

        #endregion

        #region Column Management

        /// <summary>
        /// 新增欄位到已選欄位（從可用欄位移到已選欄位）
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        private void AddColumn(string? columnName)
        {
            if (string.IsNullOrWhiteSpace(columnName))
                return;

            _columnSelectionManager.MoveToSelected(columnName);
            UpdateIndexDefinitionColumns();
            UpdateDdlPreview();
            NotifyCanExecuteChanged();
            
            // 觸發即時驗證
            TriggerRealTimeValidation();
        }

        /// <summary>
        /// 移除欄位從已選欄位（從已選欄位移回可用欄位）
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        private void RemoveColumn(string? columnName)
        {
            if (string.IsNullOrWhiteSpace(columnName))
                return;

            _columnSelectionManager.MoveToAvailable(columnName);
            UpdateIndexDefinitionColumns();
            UpdateDdlPreview();
            NotifyCanExecuteChanged();
            
            // 觸發即時驗證
            TriggerRealTimeValidation();
        }

        /// <summary>
        /// 將欄位在已選欄位中上移
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        private void MoveColumnUp(string? columnName)
        {
            if (string.IsNullOrWhiteSpace(columnName))
                return;

            _columnSelectionManager.MoveUp(columnName);
            UpdateIndexDefinitionColumns();
            UpdateDdlPreview();
            NotifyCanExecuteChanged();
        }

        /// <summary>
        /// 將欄位在已選欄位中下移
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        private void MoveColumnDown(string? columnName)
        {
            if (string.IsNullOrWhiteSpace(columnName))
                return;

            _columnSelectionManager.MoveDown(columnName);
            UpdateIndexDefinitionColumns();
            UpdateDdlPreview();
            NotifyCanExecuteChanged();
        }

        /// <summary>
        /// 檢查是否可以新增欄位
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        /// <returns>是否可以新增欄位</returns>
        private bool CanAddColumn(string? columnName)
        {
            return !string.IsNullOrWhiteSpace(columnName) && 
                   AvailableColumns.Contains(columnName) &&
                   !IsOperationInProgress &&
                   !IsLoading;
        }

        /// <summary>
        /// 檢查是否可以移除欄位
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        /// <returns>是否可以移除欄位</returns>
        private bool CanRemoveColumn(string? columnName)
        {
            return !string.IsNullOrWhiteSpace(columnName) && 
                   SelectedColumns.Contains(columnName) &&
                   !IsOperationInProgress &&
                   !IsLoading;
        }

        /// <summary>
        /// 檢查是否可以將欄位上移
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        /// <returns>是否可以將欄位上移</returns>
        private bool CanMoveColumnUp(string? columnName)
        {
            if (string.IsNullOrWhiteSpace(columnName) || IsOperationInProgress || IsLoading)
                return false;

            var index = SelectedColumns.IndexOf(columnName);
            return index > 0;
        }

        /// <summary>
        /// 檢查是否可以將欄位下移
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        /// <returns>是否可以將欄位下移</returns>
        private bool CanMoveColumnDown(string? columnName)
        {
            if (string.IsNullOrWhiteSpace(columnName) || IsOperationInProgress || IsLoading)
                return false;

            var index = SelectedColumns.IndexOf(columnName);
            return index >= 0 && index < SelectedColumns.Count - 1;
        }

        /// <summary>
        /// 根據已選欄位更新IndexDefinition的Columns
        /// </summary>
        private void UpdateIndexDefinitionColumns()
        {
            if (IndexDefinition == null)
                return;

            IndexDefinition.UpdateColumnsFromSelection(SelectedColumns);
        }

        /// <summary>
        /// 恢復索引欄位到已選欄位清單
        /// </summary>
        private void RestoreIndexColumnsToSelected()
        {
            if (IndexDefinition?.Columns?.Any() != true)
                return;

            _logger.LogInformation("開始恢復索引欄位到已選清單，欄位數量: {Count}", IndexDefinition.Columns.Count);

            // 按照原始順序恢復已選欄位
            var orderedColumns = IndexDefinition.Columns.OrderBy(c => c.Position).Select(c => c.ColumnName);
            foreach (var columnName in orderedColumns)
            {
                if (_columnSelectionManager.AvailableColumns.Contains(columnName))
                {
                    _logger.LogInformation("恢復欄位 {ColumnName} 到已選清單", columnName);
                    _columnSelectionManager.MoveToSelected(columnName);
                }
                else
                {
                    _logger.LogWarning("欄位 {ColumnName} 不在可用清單中，無法恢復", columnName);
                }
            }

            // 更新 IndexDefinition 的 Columns（確保同步）
            UpdateIndexDefinitionColumns();

            _logger.LogInformation("索引欄位恢復完成，已選欄位數量: {Count}", SelectedColumns.Count);
        }

        /// <summary>
        /// 載入可用的Schema清單（帶快取和效能優化）
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task LoadSchemasAsync()
        {
            var connection = _getConnection();
            if (connection == null)
                return;

            // 使用信號量防止並發載入
            await _loadingSemaphore.WaitAsync();
            try
            {
                IsLoadingSchemas = true;
                LoadingMessage = "正在載入Schema清單...";
                LoadingProgress = 0;

                // 檢查快取（如果啟用）
                var cacheKey = "schemas";
                if (_performanceSettings.EnableCaching &&
                    _schemaTablesCache.ContainsKey(cacheKey) &&
                    DateTime.Now - _lastCacheUpdate < TimeSpan.FromMinutes(_performanceSettings.CacheExpirationMinutes))
                {
                    LoadingProgress = 50;
                    await UpdateSchemasUI(_schemaTablesCache[cacheKey]);
                    return;
                }

                // 使用 ConfigureAwait(false) 避免死鎖，並加入取消令牌
                var schemas = await _databaseService.GetSchemasAsync(connection)
                    .ConfigureAwait(false);

                LoadingProgress = 50;

                // 更新快取
                var schemaList = schemas.OrderBy(s => s).ToList();
                _schemaTablesCache[cacheKey] = schemaList;
                _lastCacheUpdate = DateTime.Now;

                // 回到 UI 執行緒更新集合
                await UpdateSchemasUI(schemaList);
            }
            catch (OperationCanceledException)
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    StatusMessage = "載入Schema清單已取消";
                });
            }
            catch (Exception ex)
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    HandleError(ex, "載入Schema清單");
                });
            }
            finally
            {
                _loadingSemaphore.Release();
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    IsLoadingSchemas = false;
                    LoadingProgress = 0;
                });
            }
        }

        /// <summary>
        /// 更新Schema UI（分離UI更新邏輯以提高可讀性）
        /// </summary>
        private async Task UpdateSchemasUI(List<string> schemas)
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                AvailableSchemas.Clear();

                // 批次新增以提高效能，限制一次處理的數量
                var batchSize = _performanceSettings.UIUpdateBatchSize;
                for (int i = 0; i < schemas.Count; i += batchSize)
                {
                    var batch = schemas.Skip(i).Take(batchSize);
                    foreach (var schema in batch)
                    {
                        AvailableSchemas.Add(schema);
                    }

                    // 每批次後更新進度
                    LoadingProgress = 50 + (double)(i + batchSize) / schemas.Count * 50;
                }

                // 如果有預設的schema，設定為選中狀態
                if (!string.IsNullOrEmpty(_indexInfo.Schema) && AvailableSchemas.Contains(_indexInfo.Schema))
                {
                    SelectedSchema = _indexInfo.Schema;
                }

                LoadingProgress = 100;
                StatusMessage = $"已載入 {AvailableSchemas.Count} 個Schema";
            });
        }

        /// <summary>
        /// 等待 Tables 載入完成
        /// </summary>
        /// <param name="targetTableName">目標 Table 名稱</param>
        /// <returns>非同步工作</returns>
        private async Task WaitForTablesLoadedAsync(string targetTableName)
        {
            _logger.LogInformation("開始等待 Tables 載入，目標 Table: {TableName}", targetTableName);

            // 等待 Tables 載入完成，最多等待 5 秒
            var timeout = TimeSpan.FromSeconds(5);
            var startTime = DateTime.Now;

            while (DateTime.Now - startTime < timeout)
            {
                _logger.LogDebug("檢查 Tables 載入狀態，可用 Table 數量: {Count}, IsLoadingTables: {IsLoading}",
                    AvailableTables.Count, IsLoadingTables);

                // 如果不再載入中且有 Tables 資料，或者已經找到目標 Table，則完成等待
                if (!IsLoadingTables && (AvailableTables.Count > 0 || AvailableTables.Contains(targetTableName)))
                {
                    _logger.LogInformation("Tables 載入完成，可用 Table 數量: {Count}", AvailableTables.Count);
                    return;
                }

                await Task.Delay(50); // 每 50ms 檢查一次
            }

            // 如果超時，記錄警告但不拋出例外
            _logger.LogWarning("等待 Tables 載入超時，目標 Table: {TableName}，可用 Tables: [{Tables}]",
                targetTableName, string.Join(", ", AvailableTables.Take(10)));
        }

        /// <summary>
        /// 載入指定Schema下的Table清單（帶快取和效能優化）
        /// </summary>
        /// <param name="schemaName">Schema名稱</param>
        /// <returns>非同步工作</returns>
        private async Task LoadTablesForSchemaAsync(string schemaName)
        {
            if (string.IsNullOrEmpty(schemaName))
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    AvailableTables.Clear();
                    SelectedTable = string.Empty;
                });
                return;
            }

            var connection = _getConnection();
            if (connection == null)
                return;

            // 使用信號量防止並發載入
            await _loadingSemaphore.WaitAsync();
            try
            {
                IsLoadingTables = true;
                LoadingMessage = $"正在載入 {schemaName} 的Table清單...";
                LoadingProgress = 0;

                // 檢查快取
                var cacheKey = $"tables_{schemaName}";
                if (_schemaTablesCache.ContainsKey(cacheKey) &&
                    DateTime.Now - _lastCacheUpdate < TimeSpan.FromMinutes(_performanceSettings.CacheExpirationMinutes))
                {
                    LoadingProgress = 50;
                    await UpdateTablesUI(_schemaTablesCache[cacheKey], schemaName);
                    return;
                }

                // 使用新的資料庫服務方法
                var tables = await _databaseService.GetTablesBySchemaAsync(connection, schemaName).ConfigureAwait(false);

                LoadingProgress = 50;

                // 更新快取
                var tableList = tables.Select(t => t.Name).OrderBy(t => t).Take(1000).ToList();
                _schemaTablesCache[cacheKey] = tableList;
                _lastCacheUpdate = DateTime.Now;

                // 回到 UI 執行緒更新集合
                await UpdateTablesUI(tableList, schemaName, tables.Count());
            }
            catch (OperationCanceledException)
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    StatusMessage = "載入Table清單已取消";
                });
            }
            catch (Exception ex)
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    HandleError(ex, "載入Table清單");
                });
            }
            finally
            {
                _loadingSemaphore.Release();
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    IsLoadingTables = false;
                    LoadingProgress = 0;
                });
            }
        }

        /// <summary>
        /// 更新Tables UI（分離UI更新邏輯以提高可讀性）
        /// </summary>
        private async Task UpdateTablesUI(List<string> tableList, string schemaName, int? totalCount = null)
        {
            _logger.LogInformation("UpdateTablesUI 開始執行，Schema: {Schema}, Table 數量: {Count}", schemaName, tableList.Count);

            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                AvailableTables.Clear();

                // 批次新增以提高效能
                const int batchSize = 50;
                for (int i = 0; i < tableList.Count; i += batchSize)
                {
                    var batch = tableList.Skip(i).Take(batchSize);
                    foreach (var tableName in batch)
                    {
                        AvailableTables.Add(tableName);
                    }

                    // 每批次後更新進度
                    LoadingProgress = 50 + (double)(i + batchSize) / tableList.Count * 50;
                }

                LoadingProgress = 100;
                var actualTotalCount = totalCount ?? tableList.Count;
                StatusMessage = actualTotalCount > 1000
                    ? $"已載入 {AvailableTables.Count} 個Table (共 {actualTotalCount} 個，僅顯示前1000個)"
                    : $"已載入 {AvailableTables.Count} 個Table";

                _logger.LogInformation("Tables UI 更新完成，可用 Table 數量: {Count}, Tables: [{Tables}]",
                    AvailableTables.Count, string.Join(", ", AvailableTables.Take(10)));

                // 診斷模式和索引定義狀態
                _logger.LogInformation("模式檢查 - IsEditMode: {IsEditMode}, IsCreateMode: {IsCreateMode}", IsEditMode, IsCreateMode);
                _logger.LogInformation("IndexDefinition 狀態 - 是否為null: {IsNull}, TableName: {TableName}",
                    IndexDefinition == null, IndexDefinition?.TableName ?? "null");

                // 在編輯模式下，檢查是否需要自動選取 Table
                if (IsEditMode && IndexDefinition != null && !string.IsNullOrEmpty(IndexDefinition.TableName))
                {
                    _logger.LogInformation("編輯模式條件通過，檢查 Table {TableName} 是否在可用清單中", IndexDefinition.TableName);
                    if (AvailableTables.Contains(IndexDefinition.TableName))
                    {
                        _logger.LogInformation("編輯模式：自動選取 Table {TableName}", IndexDefinition.TableName);
                        SelectedTable = IndexDefinition.TableName;
                    }
                    else
                    {
                        _logger.LogWarning("編輯模式：目標 Table {TableName} 不在可用清單中", IndexDefinition.TableName);
                    }
                }
                // 在創建模式下，檢查 _indexInfo.TableName
                else if (IsCreateMode && !string.IsNullOrEmpty(_indexInfo.TableName) && AvailableTables.Contains(_indexInfo.TableName))
                {
                    _logger.LogInformation("創建模式：自動選取 Table {TableName}", _indexInfo.TableName);
                    SelectedTable = _indexInfo.TableName;
                }
                else
                {
                    _logger.LogInformation("Table 自動選取條件不符合：");
                    _logger.LogInformation("  - IsEditMode: {IsEditMode}", IsEditMode);
                    _logger.LogInformation("  - IsCreateMode: {IsCreateMode}", IsCreateMode);
                    _logger.LogInformation("  - IndexDefinition != null: {NotNull}", IndexDefinition != null);
                    _logger.LogInformation("  - IndexDefinition.TableName: {TableName}", IndexDefinition?.TableName ?? "null");
                    _logger.LogInformation("  - _indexInfo.TableName: {TableName}", _indexInfo?.TableName ?? "null");
                }
            });
        }

        /// <summary>
        /// 載入指定Table的欄位清單（帶快取和效能優化）
        /// </summary>
        /// <param name="tableName">Table名稱</param>
        /// <returns>非同步工作</returns>
        private async Task LoadColumnsForTableAsync(string tableName)
        {
            _logger.LogInformation("開始載入欄位，Table: {TableName}, Schema: {Schema}", tableName, SelectedSchema);

            if (string.IsNullOrEmpty(tableName) || string.IsNullOrEmpty(SelectedSchema))
            {
                _logger.LogWarning("載入欄位失敗：Table 或 Schema 為空，Table: {TableName}, Schema: {Schema}", tableName, SelectedSchema);
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    _columnSelectionManager.AvailableColumns.Clear();
                    _columnSelectionManager.SelectedColumns.Clear();
                    UpdateIndexDefinitionColumns();
                });
                return;
            }

            var connection = _getConnection();
            if (connection == null)
            {
                _logger.LogError("載入欄位失敗：無法取得資料庫連線");
                return;
            }

            // 使用信號量防止並發載入
            await _loadingSemaphore.WaitAsync();
            try
            {
                IsLoadingColumns = true;
                LoadingMessage = $"正在載入 {tableName} 的欄位清單...";
                LoadingProgress = 0;

                _logger.LogInformation("開始查詢欄位資訊，Schema.Table: {Schema}.{Table}", SelectedSchema, tableName);

                // 檢查快取
                var cacheKey = $"columns_{SelectedSchema}_{tableName}";
                if (_tableColumnsCache.ContainsKey(cacheKey) &&
                    DateTime.Now - _lastCacheUpdate < TimeSpan.FromMinutes(_performanceSettings.CacheExpirationMinutes))
                {
                    _logger.LogInformation("使用快取的欄位資料，欄位數量: {Count}", _tableColumnsCache[cacheKey].Count);
                    LoadingProgress = 50;
                    await UpdateColumnsUI(_tableColumnsCache[cacheKey]);
                    return;
                }

                // 使用 TableSchema 來獲取欄位資訊，包含更多詳細資訊
                _logger.LogInformation("查詢資料庫欄位資訊: {Schema}.{Table}", SelectedSchema, tableName);
                var tableSchema = await _databaseService.GetTableSchemaAsync(connection, $"{tableName}").ConfigureAwait(false);

                _logger.LogInformation("查詢完成，欄位數量: {Count}", tableSchema?.Columns?.Count ?? 0);
                LoadingProgress = 50;

                if (tableSchema?.Columns == null || !tableSchema.Columns.Any())
                {
                    _logger.LogWarning("查詢結果為空，沒有找到欄位資訊");
                    await UpdateColumnsUI(new List<string>());
                    return;
                }

                // 更新快取
                var columns = tableSchema.Columns.OrderBy(c => c.Position).Select(c => c.ColumnName).ToList();
                _logger.LogInformation("處理欄位清單，欄位: [{Columns}]", string.Join(", ", columns.Take(10)));
                _tableColumnsCache[cacheKey] = columns;
                _lastCacheUpdate = DateTime.Now;

                // 回到 UI 執行緒更新集合
                await UpdateColumnsUI(columns);
            }
            catch (OperationCanceledException)
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    StatusMessage = "載入欄位清單已取消";
                });
            }
            catch (Exception ex)
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    HandleError(ex, "載入欄位清單");
                });
            }
            finally
            {
                _loadingSemaphore.Release();
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    IsLoadingColumns = false;
                    LoadingProgress = 0;
                });
            }
        }

        /// <summary>
        /// 更新Columns UI（分離UI更新邏輯以提高可讀性）
        /// </summary>
        private async Task UpdateColumnsUI(List<string> columns)
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                _columnSelectionManager.AvailableColumns.Clear();
                _columnSelectionManager.SelectedColumns.Clear();

                // 批次新增欄位
                const int batchSize = 20;
                for (int i = 0; i < columns.Count; i += batchSize)
                {
                    var batch = columns.Skip(i).Take(batchSize);
                    foreach (var columnName in batch)
                    {
                        _columnSelectionManager.AvailableColumns.Add(columnName);
                    }

                    // 每批次後更新進度
                    LoadingProgress = 50 + (double)(i + batchSize) / columns.Count * 30;
                }

                _logger.LogInformation("Columns UI 更新完成，可用 Column 數量: {Count}, Columns: [{Columns}]",
                    _columnSelectionManager.AvailableColumns.Count,
                    string.Join(", ", _columnSelectionManager.AvailableColumns.Take(10)));

                // 如果是編輯模式且有預設的欄位，將它們移到已選欄位
                if (IsEditMode && IndexDefinition?.Columns?.Any() == true)
                {
                    _logger.LogInformation("編輯模式：在 UpdateColumnsUI 中恢復索引欄位");
                    RestoreIndexColumnsToSelected();
                }
                // 如果是創建模式且有預設的欄位，將它們移到已選欄位
                else if (IsCreateMode && _indexInfo.Columns?.Any() == true)
                {
                    _logger.LogInformation("創建模式：恢復預設欄位，欄位數量: {Count}", _indexInfo.Columns.Count);

                    // 按照原始順序恢復已選欄位
                    foreach (var columnName in _indexInfo.Columns)
                    {
                        if (_columnSelectionManager.AvailableColumns.Contains(columnName))
                        {
                            _logger.LogInformation("創建模式：移動欄位 {ColumnName} 到已選清單", columnName);
                            _columnSelectionManager.MoveToSelected(columnName);
                        }
                        else
                        {
                            _logger.LogWarning("創建模式：欄位 {ColumnName} 不在可用清單中", columnName);
                        }
                    }
                }

                UpdateIndexDefinitionColumns();
                LoadingProgress = 100;
                StatusMessage = $"已載入 {_columnSelectionManager.AvailableColumns.Count + _columnSelectionManager.SelectedColumns.Count} 個欄位";
            });
        }

        /// <summary>
        /// 載入索引定義（優化版本，包含背景載入和進度報告）
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task LoadObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            _isInitializing = true;
            try
            {
                // 使用進度報告器
                var progress = new Progress<(string message, double percentage)>(report =>
                {
                    LoadingMessage = report.message;
                    LoadingProgress = report.percentage;
                });

                // 階段1: 載入Schema清單 (0-30%)
                ((IProgress<(string, double)>)progress).Report(("正在載入Schema清單...", 0));
                await LoadSchemasAsync();
                ((IProgress<(string, double)>)progress).Report(("Schema清單載入完成", 30));

                if (IsEditMode)
                {
                    // 階段2: 編輯模式載入 (30-100%)
                    ((IProgress<(string, double)>)progress).Report(("正在載入索引定義...", 40));

                    // 編輯模式：載入現有索引定義
                    _logger.LogInformation("嘗試載入索引定義，IndexName: {IndexName}, Schema: {Schema}",
                        ObjectName, _indexInfo.Schema ?? "未指定");

                    IndexDefinition? definition = null;

                    // 如果有指定 Schema，優先使用 Schema.IndexName 的格式查詢
                    if (!string.IsNullOrEmpty(_indexInfo.Schema))
                    {
                        try
                        {
                            definition = await _objectEditorService.GetIndexDefinitionAsync(connection, ObjectName, _indexInfo.Schema);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "使用 Schema {Schema} 查詢索引 {IndexName} 失敗，嘗試不指定 Schema", _indexInfo.Schema, ObjectName);
                        }
                    }

                    // 如果上面的查詢失敗或沒有指定 Schema，嘗試不指定 Schema 的查詢
                    if (definition == null)
                    {
                        definition = await _objectEditorService.GetIndexDefinitionAsync(connection, ObjectName);
                    }

                    IndexDefinition = definition;

                    _logger.LogInformation("載入索引定義完成: IndexName={IndexName}, Owner={Owner}, TableName={TableName}, Columns={ColumnCount}",
                        definition.Name, definition.Owner, definition.TableName, definition.Columns?.Count ?? 0);

                    if (definition.Columns?.Any() == true)
                    {
                        _logger.LogInformation("索引欄位詳細資訊: [{Columns}]",
                            string.Join(", ", definition.Columns.Select(c => $"{c.ColumnName}({c.Position})")));
                    }
                    else
                    {
                        _logger.LogWarning("索引定義中沒有欄位資訊！");
                    }

                    // 設定 Schema 和 Table（從載入的索引定義中獲取）
                    if (!string.IsNullOrEmpty(definition.Owner))
                    {
                        _logger.LogInformation("檢查 Schema {Owner} 是否在可用清單中，可用 Schema 數量: {Count}",
                            definition.Owner, AvailableSchemas.Count);

                        if (AvailableSchemas.Contains(definition.Owner))
                        {
                            _logger.LogInformation("設定 SelectedSchema 為: {Owner}", definition.Owner);
                            SelectedSchema = definition.Owner; // 這會觸發 LoadTablesForSchemaAsync

                            // 等待 Tables 載入完成後設定 SelectedTable 並載入欄位
                            if (!string.IsNullOrEmpty(definition.TableName))
                            {
                                _logger.LogInformation("等待 Tables 載入完成，目標 Table: {TableName}", definition.TableName);
                                await WaitForTablesLoadedAsync(definition.TableName);

                                // 設定 SelectedTable 並載入欄位
                                _logger.LogInformation("設定 SelectedTable 為: {TableName}", definition.TableName);
                                SelectedTable = definition.TableName;

                                // 確保載入欄位
                                _logger.LogInformation("載入 Table 欄位: {TableName}", definition.TableName);
                                await LoadColumnsForTableAsync(definition.TableName);
                            }
                        }
                        else
                        {
                            _logger.LogWarning("Schema {Owner} 不在可用清單中", definition.Owner);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("索引定義中的 Owner 為空");
                    }

                    ((IProgress<(string, double)>)progress).Report(("索引定義載入完成", 100));
                }
                else
                {
                    // 創建模式：設定初始選擇並載入相關資料
                    ((IProgress<(string, double)>)progress).Report(("正在設定初始選擇...", 40));

                    // 設定預設的 Schema 和 Table（如果有提供）
                    if (!string.IsNullOrEmpty(_indexInfo.Schema) && AvailableSchemas.Contains(_indexInfo.Schema))
                    {
                        SelectedSchema = _indexInfo.Schema; // 這會觸發 LoadTablesForSchemaAsync
                        ((IProgress<(string, double)>)progress).Report(("Schema設定完成", 60));

                        // 如果有指定 TableName，等待 Tables 載入完成後設定 SelectedTable
                        if (!string.IsNullOrEmpty(_indexInfo.TableName))
                        {
                            // 等待 LoadTablesForSchemaAsync 完成
                            await WaitForTablesLoadedAsync(_indexInfo.TableName);

                            // 設定 SelectedTable 並載入欄位
                            _logger.LogInformation("新增模式：設定 SelectedTable 為: {TableName}", _indexInfo.TableName);
                            SelectedTable = _indexInfo.TableName;

                            // 確保載入欄位
                            _logger.LogInformation("新增模式：載入 Table 欄位: {TableName}", _indexInfo.TableName);
                            await LoadColumnsForTableAsync(_indexInfo.TableName);

                            ((IProgress<(string, double)>)progress).Report(("Table設定完成", 80));
                        }
                    }

                    ((IProgress<(string, double)>)progress).Report(("初始化完成", 100));
                }

                // 更新 DDL 預覽
                UpdateDdlPreview();
            }
            finally
            {
                _isInitializing = false;

                // 初始化完成後，通知命令重新評估可執行狀態
                // 這確保儲存按鈕在初始化完成後能正確顯示狀態
                NotifyCanExecuteChanged();
            }
        }

        /// <summary>
        /// 儲存索引定義 (索引不支援直接儲存，僅支援重建)
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task SaveObjectAsync()
        {
            // 索引不支援直接儲存，僅支援重建
            await Task.CompletedTask;
        }

        /// <summary>
        /// 產生 DDL 腳本
        /// </summary>
        /// <returns>DDL 腳本</returns>
        protected override string GenerateScript()
        {
            return GenerateIndexScript();
        }

        /// <summary>
        /// 驗證索引定義
        /// </summary>
        /// <returns>驗證結果</returns>
        protected override ValidationResult ValidateObject()
        {
            return IndexDefinition?.Validate() ?? new ValidationResult();
        }

        /// <summary>
        /// 重建索引
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task RebuildIndexAsync()
        {
            if (IsOperationInProgress || IsLoading)
                return;

            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            try
            {
                IsOperationInProgress = true;
                StatusMessage = $"正在重建索引 {ObjectName}...";
                HasError = false;
                ErrorMessage = string.Empty;

                // 重建索引
                await _objectEditorService.RebuildIndexAsync(connection, ObjectName);

                // 重新載入索引定義
                await LoadObjectAsync();

                StatusMessage = $"索引 {ObjectName} 已重建完成";
            }
            catch (Exception ex)
            {
                HandleError(ex, "重建索引");
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        /// <summary>
        /// 分析索引
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task AnalyzeIndexAsync()
        {
            if (IsOperationInProgress || IsLoading)
                return;

            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            try
            {
                IsOperationInProgress = true;
                StatusMessage = $"正在分析索引 {ObjectName}...";
                HasError = false;
                ErrorMessage = string.Empty;

                // 執行分析索引的 SQL
                var sql = $"ANALYZE INDEX {IndexDefinition.Owner}.{ObjectName} COMPUTE STATISTICS";
                await _databaseService.ExecuteNonQueryAsync(connection, sql);

                // 重新載入索引定義
                await LoadObjectAsync();

                StatusMessage = $"索引 {ObjectName} 已分析完成";
            }
            catch (Exception ex)
            {
                HandleError(ex, "分析索引");
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        /// <summary>
        /// 複製 DDL 到剪貼簿
        /// </summary>
        private void CopyDdlToClipboard()
        {
            try
            {
                System.Windows.Clipboard.SetText(DdlPreview);
                StatusMessage = "DDL 已複製到剪貼簿";
            }
            catch (Exception ex)
            {
                HandleError(ex, "複製 DDL");
            }
        }

        /// <summary>
        /// 儲存 DDL 到檔案
        /// </summary>
        private void SaveDdlToFile()
        {
            try
            {
                var dialog = new Microsoft.Win32.SaveFileDialog
                {
                    FileName = $"{ObjectName}_DDL.sql",
                    DefaultExt = ".sql",
                    Filter = "SQL 檔案 (*.sql)|*.sql|所有檔案 (*.*)|*.*"
                };

                if (dialog.ShowDialog() == true)
                {
                    System.IO.File.WriteAllText(dialog.FileName, DdlPreview);
                    StatusMessage = $"DDL 已儲存至 {dialog.FileName}";
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "儲存 DDL");
            }
        }

        /// <summary>
        /// 更新 DDL 預覽
        /// </summary>
        private void UpdateDdlPreview()
        {
            DdlPreview = GenerateIndexScript();
        }

        /// <summary>
        /// 產生索引 DDL 腳本
        /// </summary>
        /// <returns>索引 DDL 腳本</returns>
        private string GenerateIndexScript()
        {
            if (IndexDefinition == null)
                return string.Empty;

            var script = new System.Text.StringBuilder();

            // 產生 CREATE INDEX 語句
            script.AppendLine($"-- 索引定義: {IndexDefinition.Owner}.{IndexDefinition.Name}");
            script.AppendLine($"-- 表格: {IndexDefinition.TableName}");
            script.AppendLine($"-- 類型: {IndexDefinition.Type}");
            script.AppendLine($"-- 狀態: {IndexDefinition.Status}");
            script.AppendLine($"-- 最後分析時間: {IndexDefinition.LastAnalyzed:yyyy-MM-dd HH:mm:ss}");
            script.AppendLine();

            script.Append($"CREATE ");
            if (IndexDefinition.IsUnique)
                script.Append("UNIQUE ");

            script.Append($"INDEX {IndexDefinition.Owner}.{IndexDefinition.Name} ON {IndexDefinition.Owner}.{IndexDefinition.TableName} (");

            // 加入索引欄位
            var columnList = string.Empty;
            foreach (var column in IndexDefinition.Columns)
            {
                if (!string.IsNullOrEmpty(columnList))
                    columnList += ", ";

                columnList += column.ColumnName;
                if (column.IsDescending)
                    columnList += " DESC";
            }
            script.Append(columnList);
            script.AppendLine(")");

            // 加入表空間
            if (!string.IsNullOrEmpty(IndexDefinition.Tablespace))
                script.AppendLine($"TABLESPACE {IndexDefinition.Tablespace}");

            script.AppendLine(";");
            script.AppendLine();

            // 加入重建索引的語句
            script.AppendLine($"-- 重建索引");
            script.AppendLine($"ALTER INDEX {IndexDefinition.Owner}.{IndexDefinition.Name} REBUILD;");
            script.AppendLine();

            // 加入分析索引的語句
            script.AppendLine($"-- 分析索引");
            script.AppendLine($"ANALYZE INDEX {IndexDefinition.Owner}.{IndexDefinition.Name} COMPUTE STATISTICS;");

            return script.ToString();
        }

        /// <summary>
        /// 是否可以重建索引
        /// </summary>
        /// <returns>是否可以重建索引</returns>
        private bool CanRebuildIndex()
        {
            return !IsLoading && !IsOperationInProgress && _getConnection() != null;
        }

        /// <summary>
        /// 是否可以分析索引
        /// </summary>
        /// <returns>是否可以分析索引</returns>
        private bool CanAnalyzeIndex()
        {
            return !IsLoading && !IsOperationInProgress && _getConnection() != null;
        }

        /// <summary>
        /// 創建索引
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task CreateIndexAsync()
        {
            if (IsOperationInProgress || IsLoading)
                return;

            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            try
            {
                IsOperationInProgress = true;
                StatusMessage = $"正在創建索引 {IndexDefinition.Name}...";
                HasError = false;
                ErrorMessage = string.Empty;

                // 驗證索引定義
                var validationResult = ValidateIndexForCreation();
                if (!validationResult.IsValid)
                {
                    ErrorMessage = string.Join(", ", validationResult.Errors);
                    HasError = true;
                    return;
                }

                // 更新索引定義的基本資訊
                IndexDefinition.Owner = SelectedSchema;
                IndexDefinition.TableName = SelectedTable;

                // 創建索引
                await _objectEditorService.CreateIndexAsync(connection, IndexDefinition);

                StatusMessage = $"索引 {IndexDefinition.Name} 已創建完成";
                HasUnsavedChanges = false;

                // 觸發索引創建成功事件
                OnIndexCreatedSuccessfully();
            }
            catch (Exception ex)
            {
                HandleError(ex, "創建索引");
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        /// <summary>
        /// 更新索引
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task UpdateIndexAsync()
        {
            if (IsOperationInProgress || IsLoading)
                return;

            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            try
            {
                IsOperationInProgress = true;
                StatusMessage = $"正在更新索引 {IndexDefinition.Name}...";
                HasError = false;
                ErrorMessage = string.Empty;

                // 驗證索引定義
                var validationResult = ValidateIndexForUpdate();
                if (!validationResult.IsValid)
                {
                    ErrorMessage = string.Join(", ", validationResult.Errors);
                    HasError = true;
                    return;
                }

                // 更新索引
                await _objectEditorService.UpdateIndexAsync(connection, IndexDefinition);

                StatusMessage = $"索引 {IndexDefinition.Name} 已更新完成";
                HasUnsavedChanges = false;
            }
            catch (Exception ex)
            {
                HandleError(ex, "更新索引");
            }
            finally
            {
                IsOperationInProgress = false;
            }
        }

        /// <summary>
        /// 檢查是否可以創建索引
        /// </summary>
        /// <returns>是否可以創建索引</returns>
        private bool CanCreateIndex()
        {
            return IsCreateMode && 
                   !IsLoading && 
                   !IsOperationInProgress && 
                   _getConnection() != null &&
                   !string.IsNullOrWhiteSpace(SelectedSchema) &&
                   !string.IsNullOrWhiteSpace(SelectedTable) &&
                   !string.IsNullOrWhiteSpace(IndexDefinition?.Name) &&
                   SelectedColumns.Count > 0 &&
                   (!IsRealTimeValidationEnabled || CurrentValidationResult.IsValid);
        }

        /// <summary>
        /// 檢查是否可以更新索引
        /// </summary>
        /// <returns>是否可以更新索引</returns>
        private bool CanUpdateIndex()
        {
            return IsEditMode && 
                   !IsLoading && 
                   !IsOperationInProgress && 
                   _getConnection() != null &&
                   IndexDefinition != null &&
                   SelectedColumns.Count > 0 &&
                   (!IsRealTimeValidationEnabled || CurrentValidationResult.IsValid);
        }

        /// <summary>
        /// 驗證索引定義用於創建
        /// </summary>
        /// <returns>驗證結果</returns>
        private ValidationResult ValidateIndexForCreation()
        {
            var result = new ValidationResult();

            // 創建模式特殊驗證
            if (string.IsNullOrWhiteSpace(SelectedSchema))
            {
                result.AddError(IndexValidationMessages.SchemaRequiredForCreation);
            }

            if (string.IsNullOrWhiteSpace(SelectedTable))
            {
                result.AddError(IndexValidationMessages.TableRequiredForCreation);
            }

            if (string.IsNullOrWhiteSpace(IndexDefinition?.Name))
            {
                result.AddError(IndexValidationMessages.IndexNameRequired);
            }

            if (SelectedColumns.Count == 0)
            {
                result.AddError(IndexValidationMessages.AtLeastOneColumnRequired);
            }

            // 使用IndexDefinition的增強驗證方法
            if (IndexDefinition != null)
            {
                var indexValidation = IndexDefinition.Validate(ValidationMode.Create);
                if (!indexValidation.IsValid)
                {
                    result.AddErrors(indexValidation.Errors);
                }
            }

            return result;
        }

        /// <summary>
        /// 驗證索引定義用於更新
        /// </summary>
        /// <returns>驗證結果</returns>
        private ValidationResult ValidateIndexForUpdate()
        {
            var result = new ValidationResult();

            if (IndexDefinition == null)
            {
                result.AddError(IndexValidationMessages.IndexValidationFailed);
                return result;
            }

            if (SelectedColumns.Count == 0)
            {
                result.AddError(IndexValidationMessages.AtLeastOneColumnRequiredForEdit);
            }

            // 使用IndexDefinition的增強驗證方法
            var indexValidation = IndexDefinition.Validate(ValidationMode.Edit);
            if (!indexValidation.IsValid)
            {
                result.AddErrors(indexValidation.Errors);
            }

            return result;
        }

        /// <summary>
        /// 通知所有命令重新評估其可執行狀態
        /// </summary>
        protected override void NotifyCanExecuteChanged()
        {
            // 調用基類方法
            base.NotifyCanExecuteChanged();
            
            // 通知索引編輯器特有的命令
            (CreateIndexCommand as AsyncRelayCommand)?.NotifyCanExecuteChanged();
            (UpdateIndexCommand as AsyncRelayCommand)?.NotifyCanExecuteChanged();
            (AddColumnCommand as RelayCommand<string>)?.NotifyCanExecuteChanged();
            (RemoveColumnCommand as RelayCommand<string>)?.NotifyCanExecuteChanged();
            (MoveColumnUpCommand as RelayCommand<string>)?.NotifyCanExecuteChanged();
            (MoveColumnDownCommand as RelayCommand<string>)?.NotifyCanExecuteChanged();
        }

        #endregion

        #region 即時驗證

        /// <summary>
        /// 初始化即時驗證
        /// </summary>
        private void InitializeRealTimeValidation()
        {
            // 創建驗證計時器，延遲 500ms 執行驗證以避免頻繁驗證
            _validationTimer = new System.Timers.Timer(500);
            _validationTimer.Elapsed += OnValidationTimerElapsed;
            _validationTimer.AutoReset = false; // 只執行一次

            // 監聽屬性變更以觸發即時驗證
            PropertyChanged += OnPropertyChangedForValidation;
        }

        /// <summary>
        /// 屬性變更時觸發驗證
        /// </summary>
        /// <param name="sender">發送者</param>
        /// <param name="e">事件參數</param>
        private void OnPropertyChangedForValidation(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (!IsRealTimeValidationEnabled || _isInitializing)
                return;

            // 只對影響驗證的屬性進行即時驗證
            var validationTriggerProperties = new[]
            {
                nameof(SelectedSchema),
                nameof(SelectedTable),
                nameof(SelectedColumns),
                nameof(IndexDefinition)
            };

            if (validationTriggerProperties.Contains(e.PropertyName))
            {
                TriggerRealTimeValidation();
            }
        }

        /// <summary>
        /// 觸發即時驗證
        /// </summary>
        private void TriggerRealTimeValidation()
        {
            if (!IsRealTimeValidationEnabled)
                return;

            // 重置計時器
            _validationTimer?.Stop();
            _validationTimer?.Start();
        }

        /// <summary>
        /// 驗證計時器觸發事件
        /// </summary>
        /// <param name="sender">發送者</param>
        /// <param name="e">事件參數</param>
        private void OnValidationTimerElapsed(object? sender, System.Timers.ElapsedEventArgs e)
        {
            // 在 UI 執行緒上執行驗證
            System.Windows.Application.Current?.Dispatcher.BeginInvoke(new Action(PerformRealTimeValidation));
        }

        /// <summary>
        /// 執行即時驗證
        /// </summary>
        private void PerformRealTimeValidation()
        {
            try
            {
                ValidationResult result;

                if (IsCreateMode)
                {
                    result = ValidateIndexForCreation();
                }
                else if (IsEditMode)
                {
                    result = ValidateIndexForUpdate();
                }
                else
                {
                    result = IndexDefinition?.Validate() ?? new ValidationResult();
                }

                CurrentValidationResult = result;

                // 通知相關屬性變更
                OnPropertyChanged(nameof(HasValidationErrors));
                OnPropertyChanged(nameof(ValidationErrorMessage));
                OnPropertyChanged(nameof(HasValidationWarnings));
                OnPropertyChanged(nameof(ValidationWarningMessage));
                OnPropertyChanged(nameof(ValidationSummary));
                OnPropertyChanged(nameof(ErrorCount));
                OnPropertyChanged(nameof(WarningCount));
                OnPropertyChanged(nameof(ValidationStatusText));

                // 更新命令狀態
                NotifyCanExecuteChanged();
            }
            catch (Exception ex)
            {
                // 驗證過程中發生錯誤，記錄但不影響 UI
                System.Diagnostics.Debug.WriteLine($"即時驗證錯誤: {ex.Message}");
            }
        }

        #endregion

        #region 錯誤處理增強

        /// <summary>
        /// 處理操作失敗的友善錯誤提示
        /// </summary>
        /// <param name="ex">例外</param>
        /// <param name="operation">操作名稱</param>
        protected override void HandleError(Exception ex, string operation)
        {
            // 使用基類的錯誤處理
            base.HandleError(ex, operation);

            // 根據例外類型提供更友善的錯誤訊息
            var friendlyMessage = GetFriendlyErrorMessage(ex, operation);
            if (!string.IsNullOrEmpty(friendlyMessage))
            {
                ErrorMessage = friendlyMessage;
            }

            // 如果是驗證錯誤，同時更新驗證結果
            if (ex is ValidationException validationEx)
            {
                var validationResult = new ValidationResult();
                validationResult.AddError(validationEx.Message, ValidationErrorType.General);
                CurrentValidationResult = validationResult;
                
                // 通知所有驗證相關屬性變更
                OnPropertyChanged(nameof(HasValidationErrors));
                OnPropertyChanged(nameof(ValidationErrorMessage));
                OnPropertyChanged(nameof(HasValidationWarnings));
                OnPropertyChanged(nameof(ValidationWarningMessage));
                OnPropertyChanged(nameof(ValidationSummary));
                OnPropertyChanged(nameof(ErrorCount));
                OnPropertyChanged(nameof(WarningCount));
                OnPropertyChanged(nameof(ValidationStatusText));
            }

            // 記錄詳細錯誤資訊以供除錯
            LogDetailedError(ex, operation);
        }

        /// <summary>
        /// 取得友善的錯誤訊息
        /// </summary>
        /// <param name="ex">例外</param>
        /// <param name="operation">操作名稱</param>
        /// <returns>友善的錯誤訊息</returns>
        private string GetFriendlyErrorMessage(Exception ex, string operation)
        {
            return ex switch
            {
                Oracle.ManagedDataAccess.Client.OracleException oracleEx => GetOracleFriendlyMessage(oracleEx, operation),
                System.Data.Common.DbException => IndexValidationMessages.DatabaseConnectionFailed,
                UnauthorizedAccessException => IndexValidationMessages.InsufficientPermissions,
                TimeoutException => $"{operation}操作逾時，請稍後再試",
                ArgumentException argEx when argEx.Message.Contains("duplicate") => IndexValidationMessages.IndexAlreadyExists,
                _ => string.Empty
            };
        }

        /// <summary>
        /// 取得 Oracle 錯誤的友善訊息
        /// </summary>
        /// <param name="ex">Oracle 例外</param>
        /// <param name="operation">操作名稱</param>
        /// <returns>友善的錯誤訊息</returns>
        private string GetOracleFriendlyMessage(Oracle.ManagedDataAccess.Client.OracleException ex, string operation)
        {
            return ex.Number switch
            {
                // 索引相關錯誤
                955 => IndexValidationMessages.IndexAlreadyExists,
                1408 => IndexValidationMessages.IndexAlreadyExists, // 另一種索引已存在的錯誤碼
                
                // 物件不存在錯誤
                942 => IndexValidationMessages.TableNotFound,
                904 => IndexValidationMessages.ColumnNotFound,
                959 => IndexValidationMessages.TablespaceNotFound,
                
                // 權限錯誤
                1031 => IndexValidationMessages.InsufficientPermissions,
                1950 => IndexValidationMessages.InvalidTablespacePermissions,
                
                // 資料驗證錯誤
                1400 => "必填欄位不能為空",
                1722 => "數值格式錯誤",
                12899 => "欄位值過大",
                
                // 資源錯誤
                1652 => IndexValidationMessages.SystemResourceExhausted,
                1654 => IndexValidationMessages.IndexTooLarge,
                
                // 並行存取錯誤
                54 => IndexValidationMessages.ConcurrentModification,
                60 => "資源忙碌中，請稍後再試",
                
                // 網路和連線錯誤
                12170 => IndexValidationMessages.NetworkTimeout,
                12541 => IndexValidationMessages.DatabaseConnectionFailed,
                12545 => IndexValidationMessages.DatabaseConnectionFailed,
                
                // 語法錯誤
                900 => "SQL 語法錯誤",
                906 => "缺少左括號",
                907 => "缺少右括號",
                
                // 預設處理
                _ => FormatDetailedOracleError(ex, operation)
            };
        }

        /// <summary>
        /// 格式化詳細的 Oracle 錯誤訊息
        /// </summary>
        /// <param name="ex">Oracle 例外</param>
        /// <param name="operation">操作名稱</param>
        /// <returns>格式化的錯誤訊息</returns>
        private string FormatDetailedOracleError(Oracle.ManagedDataAccess.Client.OracleException ex, string operation)
        {
            var errorMessage = new System.Text.StringBuilder();
            errorMessage.AppendLine($"{operation}失敗");
            errorMessage.AppendLine($"錯誤代碼：ORA-{ex.Number:D5}");
            errorMessage.AppendLine($"錯誤訊息：{ex.Message}");
            
            // 提供可能的解決方案
            var suggestion = GetErrorSuggestion(ex.Number);
            if (!string.IsNullOrEmpty(suggestion))
            {
                errorMessage.AppendLine($"建議解決方案：{suggestion}");
            }
            
            return errorMessage.ToString().TrimEnd();
        }

        /// <summary>
        /// 根據錯誤代碼提供解決建議
        /// </summary>
        /// <param name="errorNumber">Oracle 錯誤代碼</param>
        /// <returns>解決建議</returns>
        private string GetErrorSuggestion(int errorNumber)
        {
            return errorNumber switch
            {
                955 => "請使用不同的索引名稱，或先刪除現有的同名索引",
                942 => "請確認資料表名稱正確，且您有存取該資料表的權限",
                904 => "請確認欄位名稱正確，且該欄位存在於指定的資料表中",
                1031 => "請聯絡資料庫管理員取得必要的權限",
                1652 => "請聯絡資料庫管理員增加表空間容量，或選擇其他表空間",
                54 => "請稍後再試，或等待其他會話完成對該物件的操作",
                12170 => "請檢查網路連線，並確認資料庫服務正常運作",
                _ => "請檢查錯誤詳情並聯絡系統管理員"
            };
        }

        /// <summary>
        /// 記錄詳細錯誤資訊
        /// </summary>
        /// <param name="ex">例外</param>
        /// <param name="operation">操作名稱</param>
        private void LogDetailedError(Exception ex, string operation)
        {
            try
            {
                var errorDetails = new System.Text.StringBuilder();
                errorDetails.AppendLine($"操作：{operation}");
                errorDetails.AppendLine($"時間：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                errorDetails.AppendLine($"例外類型：{ex.GetType().Name}");
                errorDetails.AppendLine($"錯誤訊息：{ex.Message}");
                
                if (ex is Oracle.ManagedDataAccess.Client.OracleException oracleEx)
                {
                    errorDetails.AppendLine($"Oracle 錯誤代碼：ORA-{oracleEx.Number:D5}");
                    errorDetails.AppendLine($"錯誤來源：{oracleEx.Source}");
                }
                
                if (ex.InnerException != null)
                {
                    errorDetails.AppendLine($"內部例外：{ex.InnerException.Message}");
                }
                
                errorDetails.AppendLine($"堆疊追蹤：{ex.StackTrace}");
                
                // 記錄到除錯輸出
                System.Diagnostics.Debug.WriteLine($"IndexEditor 錯誤詳情:\n{errorDetails}");
                
                // 如果有 logger，也記錄到日誌系統
                // _logger?.LogError(ex, "IndexEditor operation failed: {Operation}", operation);
            }
            catch
            {
                // 避免記錄錯誤時發生例外影響主要流程
                System.Diagnostics.Debug.WriteLine($"IndexEditor 錯誤記錄失敗: {operation}");
            }
        }

        #endregion

        #region IDisposable 擴展

        /// <summary>
        /// 釋放資源時的清理邏輯
        /// </summary>
        protected override void OnDisposing()
        {
            base.OnDisposing();

            // 停止並釋放驗證計時器
            if (_validationTimer != null)
            {
                _validationTimer.Stop();
                _validationTimer.Elapsed -= OnValidationTimerElapsed;
                _validationTimer.Dispose();
                _validationTimer = null;
            }

            // 取消屬性變更監聽
            PropertyChanged -= OnPropertyChangedForValidation;
        }

        /// <summary>
        /// 取消所有載入操作
        /// </summary>
        public void CancelLoadingOperations()
        {
            _loadingCancellationTokenSource?.Cancel();

            System.Windows.Application.Current?.Dispatcher.InvokeAsync(() =>
            {
                IsLoadingSchemas = false;
                IsLoadingTables = false;
                IsLoadingColumns = false;
                IsOperationInProgress = false;
                LoadingProgress = 0;
                StatusMessage = "操作已取消";
            });
        }



        /// <summary>
        /// 初始化效能監控計時器
        /// </summary>
        private void InitializePerformanceMonitoring()
        {
            if (!_performanceSettings.EnablePerformanceMonitoring)
                return;

            _performanceMonitorTimer = new System.Timers.Timer(_performanceSettings.PerformanceMonitorIntervalMs);
            _performanceMonitorTimer.Elapsed += (sender, e) =>
            {
                // 在 UI 執行緒上更新效能監控資訊
                System.Windows.Application.Current?.Dispatcher.InvokeAsync(() =>
                {
                    OnPropertyChanged(nameof(CachedSchemasCount));
                    OnPropertyChanged(nameof(CachedTablesCount));
                    OnPropertyChanged(nameof(MemoryUsageText));

                    // 定期清理過期快取
                    CleanupCache();
                });
            };
            _performanceMonitorTimer.Start();
        }







        /// <summary>
        /// 釋放資源（增強版本，包含快取清理和記憶體優化）
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 取消所有進行中的操作
                CancelLoadingOperations();

                // 釋放計時器
                _validationTimer?.Stop();
                _validationTimer?.Dispose();
                _validationTimer = null;

                _performanceMonitorTimer?.Stop();
                _performanceMonitorTimer?.Dispose();
                _performanceMonitorTimer = null;

                // 釋放取消令牌
                _loadingCancellationTokenSource?.Dispose();
                _loadingCancellationTokenSource = null;

                // 釋放信號量
                _loadingSemaphore?.Dispose();

                // 清空快取以釋放記憶體
                _tableColumnsCache?.Clear();
                _schemaTablesCache?.Clear();

                // 清空集合以釋放記憶體
                AvailableSchemas?.Clear();
                AvailableTables?.Clear();
                // ColumnSelectionManager 沒有 Dispose 方法，只需要清空即可

                // 強制垃圾回收（僅在必要時）
                if (_tableColumnsCache?.Count > 100 || _schemaTablesCache?.Count > 50)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
            }

            base.Dispose(disposing);
        }

        /// <summary>
        /// 清理快取（定期清理過期的快取項目）
        /// </summary>
        private void CleanupCache()
        {
            if (!_performanceSettings.EnableCaching)
                return;

            var cacheExpiration = TimeSpan.FromMinutes(_performanceSettings.CacheExpirationMinutes);
            if (DateTime.Now - _lastCacheUpdate > cacheExpiration)
            {
                _tableColumnsCache.Clear();
                _schemaTablesCache.Clear();
                _lastCacheUpdate = DateTime.MinValue;

                // 記錄快取清理
                if (_performanceSettings.EnableVerboseLogging)
                    _logger.LogDebug("快取已清理，釋放記憶體");
            }

            // 如果快取項目過多，清理最舊的項目
            if (_tableColumnsCache.Count > _performanceSettings.MaxCacheItems)
            {
                var itemsToRemove = _tableColumnsCache.Count - _performanceSettings.MaxCacheItems;
                var keysToRemove = _tableColumnsCache.Keys.Take(itemsToRemove).ToList();
                foreach (var key in keysToRemove)
                {
                    _tableColumnsCache.Remove(key);
                }
            }

            if (_schemaTablesCache.Count > _performanceSettings.MaxCacheItems)
            {
                var itemsToRemove = _schemaTablesCache.Count - _performanceSettings.MaxCacheItems;
                var keysToRemove = _schemaTablesCache.Keys.Take(itemsToRemove).ToList();
                foreach (var key in keysToRemove)
                {
                    _schemaTablesCache.Remove(key);
                }
            }
        }

        /// <summary>
        /// 獲取記憶體使用情況（用於監控和調試）
        /// </summary>
        private static long GetMemoryUsage()
        {
            return GC.GetTotalMemory(false);
        }

        /// <summary>
        /// 優化記憶體使用（在大量資料載入後調用）
        /// </summary>
        private void OptimizeMemoryUsage()
        {
            if (!_performanceSettings.EnableMemoryOptimization)
                return;

            // 清理過期快取
            CleanupCache();

            // 如果記憶體使用過高，強制垃圾回收
            var memoryBefore = GetMemoryUsage();
            var thresholdBytes = _performanceSettings.MemoryThresholdMB * 1024 * 1024;

            if (memoryBefore > thresholdBytes)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                var memoryAfter = GetMemoryUsage();

                if (_performanceSettings.EnableVerboseLogging)
                {
                    _logger.LogDebug("記憶體優化完成: {MemoryBefore}MB -> {MemoryAfter}MB",
                        memoryBefore / 1024 / 1024, memoryAfter / 1024 / 1024);
                }
            }
        }



        /// <summary>
        /// 預載入常用資料（提升使用者體驗）
        /// </summary>
        private async Task PreloadCommonDataAsync()
        {
            try
            {
                // 在背景預載入最常用的Schema的Tables
                var commonSchemas = new[] { "PUBLIC", "SYS", "SYSTEM" };

                foreach (var schema in commonSchemas)
                {
                    if (AvailableSchemas.Contains(schema))
                    {
                        // 預載入到快取中，但不更新UI
                        var cacheKey = $"tables_{schema}";
                        if (!_schemaTablesCache.ContainsKey(cacheKey))
                        {
                            var connection = _getConnection();
                            if (connection != null)
                            {
                                var tables = await _databaseService.GetTablesBySchemaAsync(connection, schema).ConfigureAwait(false);
                                var tableList = tables.Select(t => t.Name).OrderBy(t => t).Take(1000).ToList();
                                _schemaTablesCache[cacheKey] = tableList;
                            }
                        }
                    }
                }

                _logger.LogDebug("常用資料預載入完成");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "預載入常用資料時發生錯誤");
            }
        }

        /// <summary>
        /// 獲取效能統計資訊
        /// </summary>
        /// <returns>效能統計字典</returns>
        public Dictionary<string, object> GetPerformanceStatistics()
        {
            return new Dictionary<string, object>
            {
                ["CachedSchemasCount"] = CachedSchemasCount,
                ["CachedTablesCount"] = CachedTablesCount,
                ["MemoryUsageMB"] = GetMemoryUsage() / 1024 / 1024,
                ["CacheExpirationMinutes"] = _performanceSettings.CacheExpirationMinutes,
                ["MaxDisplayTables"] = _performanceSettings.MaxDisplayTables,
                ["MaxDisplayColumns"] = _performanceSettings.MaxDisplayColumns,
                ["UIUpdateBatchSize"] = _performanceSettings.UIUpdateBatchSize,
                ["ValidationDelayMs"] = _performanceSettings.ValidationDelayMs,
                ["PerformanceMonitorIntervalMs"] = _performanceSettings.PerformanceMonitorIntervalMs,
                ["EnableBackgroundPreloading"] = _performanceSettings.EnableBackgroundPreloading,
                ["EnableMemoryOptimization"] = _performanceSettings.EnableMemoryOptimization,
                ["MemoryThresholdMB"] = _performanceSettings.MemoryThresholdMB,
                ["EnableUIVirtualization"] = _performanceSettings.EnableUIVirtualization,
                ["EnableCaching"] = _performanceSettings.EnableCaching,
                ["MaxCacheItems"] = _performanceSettings.MaxCacheItems,
                ["EnablePerformanceMonitoring"] = _performanceSettings.EnablePerformanceMonitoring,
                ["EnableVerboseLogging"] = _performanceSettings.EnableVerboseLogging,
                ["LoadingTimeoutSeconds"] = _performanceSettings.LoadingTimeoutSeconds,
                ["LastCacheUpdate"] = _lastCacheUpdate,
                ["IsAnyLoadingInProgress"] = IsAnyLoadingInProgress,
                ["IsRealTimeValidationEnabled"] = IsRealTimeValidationEnabled
            };
        }

        /// <summary>
        /// 重設效能統計
        /// </summary>
        public void ResetPerformanceStatistics()
        {
            _tableColumnsCache.Clear();
            _schemaTablesCache.Clear();
            _lastCacheUpdate = DateTime.MinValue;

            // 強制垃圾回收
            if (_performanceSettings.EnableMemoryOptimization)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }

            if (_performanceSettings.EnableVerboseLogging)
            {
                _logger.LogInformation("效能統計已重設");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// 觸發索引創建成功事件
        /// </summary>
        private void OnIndexCreatedSuccessfully()
        {
            var eventArgs = new IndexCreatedEventArgs(
                IndexDefinition.Owner,
                IndexDefinition.TableName,
                IndexDefinition.Name);

            System.Diagnostics.Debug.WriteLine($"[IndexEditor] 觸發索引創建成功事件: Schema={eventArgs.Schema}, Table={eventArgs.TableName}, Index={eventArgs.IndexName}");

            IndexCreatedSuccessfully?.Invoke(this, eventArgs);
        }

        #endregion
    }

    /// <summary>
    /// 索引創建成功事件參數
    /// </summary>
    public class IndexCreatedEventArgs : EventArgs
    {
        /// <summary>
        /// Schema 名稱
        /// </summary>
        public string Schema { get; }

        /// <summary>
        /// 資料表名稱
        /// </summary>
        public string TableName { get; }

        /// <summary>
        /// 索引名稱
        /// </summary>
        public string IndexName { get; }

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="schema">Schema 名稱</param>
        /// <param name="tableName">資料表名稱</param>
        /// <param name="indexName">索引名稱</param>
        public IndexCreatedEventArgs(string schema, string tableName, string indexName)
        {
            Schema = schema ?? throw new ArgumentNullException(nameof(schema));
            TableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
            IndexName = indexName ?? throw new ArgumentNullException(nameof(indexName));
        }
    }
}